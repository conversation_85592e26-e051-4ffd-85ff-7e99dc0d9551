#!/usr/bin/env python3

import requests
import json

def check_leave_policies():
    """Check if leave policies are available via API"""
    
    base_url = "http://localhost:8085"
    
    # First, let's try to get a token
    print("🔐 Getting authentication token...")
    
    # Try to login as admin
    login_data = {
        "email": "<EMAIL>",
        "password": "admin123"
    }
    
    try:
        response = requests.post(f"{base_url}/api/auth/login", json=login_data)
        print(f"Login Status: {response.status_code}")
        
        if response.status_code == 200:
            token_data = response.json()
            token = token_data.get('access_token')
            print(f"✅ Got token: {token[:20]}...")
            
            # Now test the leave policies endpoint
            headers = {"Authorization": f"Bearer {token}"}
            
            print("\n📋 Testing GET /api/leave/policies...")
            response = requests.get(f"{base_url}/api/leave/policies", headers=headers)
            
            print(f"Status Code: {response.status_code}")
            print(f"Response Headers: {dict(response.headers)}")
            
            if response.status_code == 200:
                policies = response.json()
                print(f"✅ Found {len(policies)} leave policies:")
                
                for policy in policies:
                    print(f"  - {policy['name']} ({policy['leave_type']}) - {policy['annual_entitlement']} days/year")
                    print(f"    ID: {policy['id']}")
                    print(f"    Active: {policy['is_active']}")
                    
            else:
                print(f"❌ Error: {response.status_code}")
                print(f"Response: {response.text}")
                
        else:
            print(f"❌ Login failed: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    check_leave_policies()
