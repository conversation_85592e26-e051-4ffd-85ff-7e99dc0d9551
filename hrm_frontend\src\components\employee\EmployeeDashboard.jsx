/**
 * Professional Employee Management Dashboard
 * Features: Analytics, quick stats, recent activity, employee overview
 * Premium UI/UX with enhanced design patterns
 */

import React, { useState, useEffect } from 'react';
import {
  Users,
  UserPlus,
  UserCheck,
  UserX,
  TrendingUp,
  TrendingDown,
  Calendar,
  Clock,
  Award,
  Target,
  BarChart3,
  PieChart,
  Activity,
  Filter,
  Download,
  Search,
  Plus,
  Eye,
  Edit,
  MoreVertical,
  MapPin,
  Mail,
  Phone,
  Briefcase,
  Star,
  ArrowUp,
  ArrowDown
} from 'lucide-react';
import { usePermissions } from '../../hooks/usePermissions';
import { PermissionGate } from '../ProtectedRoute';
import apiService from '../../services/api';

const EmployeeDashboard = () => {
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [selectedView, setSelectedView] = useState('overview');
  const [selectedTimeframe, setSelectedTimeframe] = useState('month');
  const permissions = usePermissions();

  useEffect(() => {
    loadDashboardData();
  }, [selectedTimeframe]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      // Try to fetch from API, fallback to mock data
      const data = await apiService.get('/employee/dashboard').catch(() => null);
      setDashboardData(data || getMockDashboardData());
    } catch (error) {
      console.error('Error loading dashboard data:', error);
      setDashboardData(getMockDashboardData());
    } finally {
      setLoading(false);
    }
  };

  const getMockDashboardData = () => ({
    summary: {
      total_employees: 156,
      active_employees: 142,
      new_hires_this_month: 8,
      pending_onboarding: 3,
      departments: 12,
      average_tenure: '2.3 years',
      employee_satisfaction: 4.2,
      retention_rate: 94.5
    },
    growth_metrics: {
      employee_growth: 12.5,
      new_hires_trend: 8.3,
      retention_trend: -2.1,
      satisfaction_trend: 5.7
    },
    recent_activities: [
      {
        id: 1,
        type: 'new_hire',
        employee: 'Sarah Johnson',
        department: 'Engineering',
        date: '2024-01-15',
        status: 'completed'
      },
      {
        id: 2,
        type: 'promotion',
        employee: 'Michael Chen',
        department: 'Marketing',
        date: '2024-01-14',
        status: 'pending'
      },
      {
        id: 3,
        type: 'transfer',
        employee: 'Emily Davis',
        department: 'HR',
        date: '2024-01-13',
        status: 'completed'
      }
    ],
    department_breakdown: [
      { name: 'Engineering', count: 45, percentage: 28.8 },
      { name: 'Sales', count: 32, percentage: 20.5 },
      { name: 'Marketing', count: 28, percentage: 17.9 },
      { name: 'HR', count: 18, percentage: 11.5 },
      { name: 'Finance', count: 15, percentage: 9.6 },
      { name: 'Operations', count: 18, percentage: 11.5 }
    ],
    top_performers: [
      {
        id: 1,
        name: 'Alex Rodriguez',
        department: 'Sales',
        performance_score: 98,
        avatar: null
      },
      {
        id: 2,
        name: 'Jessica Wong',
        department: 'Engineering',
        performance_score: 96,
        avatar: null
      },
      {
        id: 3,
        name: 'David Kim',
        department: 'Marketing',
        performance_score: 94,
        avatar: null
      }
    ]
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-agno-primary"></div>
      </div>
    );
  }

  const { summary, growth_metrics, recent_activities, department_breakdown, top_performers } = dashboardData || {};

  const StatCard = ({ title, value, subtitle, icon: Icon, trend, trendValue, color = 'blue' }) => {
    const colorClasses = {
      blue: 'bg-blue-50 text-blue-600 border-blue-200',
      green: 'bg-green-50 text-green-600 border-green-200',
      orange: 'bg-orange-50 text-orange-600 border-orange-200',
      purple: 'bg-purple-50 text-purple-600 border-purple-200',
      red: 'bg-red-50 text-red-600 border-red-200'
    };

    return (
      <div className="bg-white rounded-xl shadow-agno border border-gray-100 p-6 hover:shadow-agno-lg transition-all duration-300">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-3">
              <div className={`p-3 rounded-lg ${colorClasses[color]}`}>
                <Icon size={24} />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-600">{title}</p>
                <p className="text-2xl font-bold text-gray-900">{value}</p>
                {subtitle && (
                  <p className="text-sm text-gray-500">{subtitle}</p>
                )}
              </div>
            </div>
          </div>
          {trend && (
            <div className={`flex items-center space-x-1 ${trend === 'up' ? 'text-green-600' : 'text-red-600'}`}>
              {trend === 'up' ? <ArrowUp size={16} /> : <ArrowDown size={16} />}
              <span className="text-sm font-medium">{trendValue}%</span>
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-8">
      {/* Header Section */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Employee Management</h1>
          <p className="text-gray-600">Comprehensive overview of your workforce and HR metrics</p>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-3">
          <div className="flex items-center space-x-2">
            <select
              value={selectedTimeframe}
              onChange={(e) => setSelectedTimeframe(e.target.value)}
              className="form-input text-sm"
            >
              <option value="week">This Week</option>
              <option value="month">This Month</option>
              <option value="quarter">This Quarter</option>
              <option value="year">This Year</option>
            </select>
          </div>
          
          <PermissionGate permission="employeeDirectory">
            <button className="btn-agno-primary flex items-center space-x-2 px-4 py-2 rounded-lg">
              <UserPlus size={16} />
              <span>Add Employee</span>
            </button>
          </PermissionGate>
        </div>
      </div>

      {/* Key Metrics Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Total Employees"
          value={summary?.total_employees || 0}
          subtitle={`${summary?.active_employees || 0} active`}
          icon={Users}
          trend="up"
          trendValue={growth_metrics?.employee_growth}
          color="blue"
        />
        <StatCard
          title="New Hires"
          value={summary?.new_hires_this_month || 0}
          subtitle="This month"
          icon={UserPlus}
          trend="up"
          trendValue={growth_metrics?.new_hires_trend}
          color="green"
        />
        <StatCard
          title="Retention Rate"
          value={`${summary?.retention_rate || 0}%`}
          subtitle="Last 12 months"
          icon={Target}
          trend={growth_metrics?.retention_trend > 0 ? 'up' : 'down'}
          trendValue={Math.abs(growth_metrics?.retention_trend)}
          color="purple"
        />
        <StatCard
          title="Satisfaction"
          value={`${summary?.employee_satisfaction || 0}/5`}
          subtitle="Average rating"
          icon={Star}
          trend="up"
          trendValue={growth_metrics?.satisfaction_trend}
          color="orange"
        />
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Left Column - Charts and Analytics */}
        <div className="lg:col-span-2 space-y-8">
          {/* Department Breakdown Chart */}
          <div className="bg-white rounded-xl shadow-agno border border-gray-100 p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">Department Distribution</h3>
              <button className="text-gray-400 hover:text-gray-600">
                <MoreVertical size={20} />
              </button>
            </div>
            <div className="space-y-4">
              {department_breakdown?.map((dept, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`w-3 h-3 rounded-full bg-${['blue', 'green', 'orange', 'purple', 'red', 'yellow'][index % 6]}-500`}></div>
                    <span className="text-sm font-medium text-gray-700">{dept.name}</span>
                  </div>
                  <div className="flex items-center space-x-3">
                    <span className="text-sm text-gray-500">{dept.count} employees</span>
                    <span className="text-sm font-medium text-gray-900">{dept.percentage}%</span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Recent Activities */}
          <div className="bg-white rounded-xl shadow-agno border border-gray-100 p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">Recent Activities</h3>
              <button className="text-sm text-agno-primary hover:text-agno-primary-dark font-medium">
                View All
              </button>
            </div>
            <div className="space-y-4">
              {recent_activities?.map((activity) => (
                <div key={activity.id} className="flex items-center space-x-4 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                  <div className={`p-2 rounded-full ${
                    activity.type === 'new_hire' ? 'bg-green-100 text-green-600' :
                    activity.type === 'promotion' ? 'bg-blue-100 text-blue-600' :
                    'bg-orange-100 text-orange-600'
                  }`}>
                    {activity.type === 'new_hire' ? <UserPlus size={16} /> :
                     activity.type === 'promotion' ? <TrendingUp size={16} /> :
                     <Activity size={16} />}
                  </div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-900">{activity.employee}</p>
                    <p className="text-xs text-gray-500">
                      {activity.type === 'new_hire' ? 'Joined' :
                       activity.type === 'promotion' ? 'Promoted in' :
                       'Transferred to'} {activity.department}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-xs text-gray-500">{activity.date}</p>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      activity.status === 'completed' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {activity.status}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Right Column - Quick Stats and Actions */}
        <div className="space-y-8">
          {/* Quick Actions */}
          <div className="bg-white rounded-xl shadow-agno border border-gray-100 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
            <div className="space-y-3">
              <PermissionGate permission="employeeDirectory">
                <button className="w-full flex items-center space-x-3 p-3 text-left rounded-lg hover:bg-gray-50 transition-colors">
                  <UserPlus className="text-blue-600" size={20} />
                  <span className="font-medium text-gray-700">Add New Employee</span>
                </button>
              </PermissionGate>
              <button className="w-full flex items-center space-x-3 p-3 text-left rounded-lg hover:bg-gray-50 transition-colors">
                <Download className="text-green-600" size={20} />
                <span className="font-medium text-gray-700">Export Employee Data</span>
              </button>
              <button className="w-full flex items-center space-x-3 p-3 text-left rounded-lg hover:bg-gray-50 transition-colors">
                <BarChart3 className="text-purple-600" size={20} />
                <span className="font-medium text-gray-700">Generate Reports</span>
              </button>
              <button className="w-full flex items-center space-x-3 p-3 text-left rounded-lg hover:bg-gray-50 transition-colors">
                <Calendar className="text-orange-600" size={20} />
                <span className="font-medium text-gray-700">Schedule Review</span>
              </button>
            </div>
          </div>

          {/* Top Performers */}
          <div className="bg-white rounded-xl shadow-agno border border-gray-100 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Top Performers</h3>
              <Award className="text-yellow-500" size={20} />
            </div>
            <div className="space-y-4">
              {top_performers?.map((performer, index) => (
                <div key={performer.id} className="flex items-center space-x-3">
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-blue-600 rounded-full flex items-center justify-center text-white font-semibold text-sm">
                      {performer.name.split(' ').map(n => n[0]).join('')}
                    </div>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">{performer.name}</p>
                    <p className="text-xs text-gray-500">{performer.department}</p>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Star className="text-yellow-400" size={14} />
                    <span className="text-sm font-medium text-gray-900">{performer.performance_score}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Additional Metrics */}
          <div className="bg-white rounded-xl shadow-agno border border-gray-100 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Additional Metrics</h3>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Average Tenure</span>
                <span className="text-sm font-medium text-gray-900">{summary?.average_tenure}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Departments</span>
                <span className="text-sm font-medium text-gray-900">{summary?.departments}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Pending Onboarding</span>
                <span className="text-sm font-medium text-gray-900">{summary?.pending_onboarding}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmployeeDashboard;
