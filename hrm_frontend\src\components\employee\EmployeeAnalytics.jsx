/**
 * Employee Analytics Component
 * Features: Advanced charts, metrics, and reporting
 * Premium UI/UX with professional design
 */

import React, { useState, useEffect } from 'react';
import {
  BarChart3,
  Pie<PERSON>hart,
  TrendingUp,
  TrendingDown,
  Users,
  UserPlus,
  UserMinus,
  Calendar,
  Clock,
  Award,
  Target,
  Activity,
  Download,
  Filter,
  RefreshCw,
  ArrowUp,
  ArrowDown,
  Eye,
  Building,
  Star
} from 'lucide-react';
import apiService from '../../services/api';

const EmployeeAnalytics = () => {
  const [analyticsData, setAnalyticsData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [selectedTimeframe, setSelectedTimeframe] = useState('month');
  const [selectedMetric, setSelectedMetric] = useState('overview');

  useEffect(() => {
    loadAnalyticsData();
  }, [selectedTimeframe]);

  const loadAnalyticsData = async () => {
    try {
      setLoading(true);
      const data = await apiService.get(`/employee/analytics?timeframe=${selectedTimeframe}`).catch(() => null);
      setAnalyticsData(data || getMockAnalyticsData());
    } catch (error) {
      console.error('Error loading analytics data:', error);
      setAnalyticsData(getMockAnalyticsData());
    } finally {
      setLoading(false);
    }
  };

  const getMockAnalyticsData = () => ({
    overview: {
      total_employees: 156,
      active_employees: 142,
      new_hires: 8,
      departures: 3,
      growth_rate: 12.5,
      retention_rate: 94.5,
      average_tenure: 2.3,
      satisfaction_score: 4.2
    },
    trends: {
      employee_growth: [
        { month: 'Jan', count: 140 },
        { month: 'Feb', count: 145 },
        { month: 'Mar', count: 148 },
        { month: 'Apr', count: 152 },
        { month: 'May', count: 156 }
      ],
      hiring_trends: [
        { month: 'Jan', hires: 5, departures: 2 },
        { month: 'Feb', hires: 7, departures: 1 },
        { month: 'Mar', hires: 6, departures: 3 },
        { month: 'Apr', hires: 8, departures: 2 },
        { month: 'May', hires: 8, departures: 3 }
      ]
    },
    departments: [
      { name: 'Engineering', count: 45, percentage: 28.8, growth: 15.2 },
      { name: 'Sales', count: 32, percentage: 20.5, growth: 8.7 },
      { name: 'Marketing', count: 28, percentage: 17.9, growth: 12.1 },
      { name: 'HR', count: 18, percentage: 11.5, growth: 5.3 },
      { name: 'Finance', count: 15, percentage: 9.6, growth: 3.2 },
      { name: 'Operations', count: 18, percentage: 11.5, growth: 7.8 }
    ],
    performance_metrics: {
      top_performers: [
        { name: 'Alex Rodriguez', department: 'Sales', score: 98 },
        { name: 'Jessica Wong', department: 'Engineering', score: 96 },
        { name: 'David Kim', department: 'Marketing', score: 94 }
      ],
      average_performance: 4.2,
      performance_distribution: [
        { range: '4.5-5.0', count: 45, percentage: 28.8 },
        { range: '4.0-4.4', count: 62, percentage: 39.7 },
        { range: '3.5-3.9', count: 35, percentage: 22.4 },
        { range: '3.0-3.4', count: 12, percentage: 7.7 },
        { range: '< 3.0', count: 2, percentage: 1.3 }
      ]
    },
    diversity_metrics: {
      gender_distribution: [
        { category: 'Male', count: 89, percentage: 57.1 },
        { category: 'Female', count: 65, percentage: 41.7 },
        { category: 'Other', count: 2, percentage: 1.3 }
      ],
      age_distribution: [
        { range: '20-25', count: 23, percentage: 14.7 },
        { range: '26-30', count: 45, percentage: 28.8 },
        { range: '31-35', count: 38, percentage: 24.4 },
        { range: '36-40', count: 28, percentage: 17.9 },
        { range: '40+', count: 22, percentage: 14.1 }
      ]
    }
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-agno-primary"></div>
      </div>
    );
  }

  const MetricCard = ({ title, value, subtitle, icon: Icon, trend, trendValue, color = 'blue' }) => {
    const colorClasses = {
      blue: 'bg-blue-50 text-blue-600 border-blue-200',
      green: 'bg-green-50 text-green-600 border-green-200',
      orange: 'bg-orange-50 text-orange-600 border-orange-200',
      purple: 'bg-purple-50 text-purple-600 border-purple-200',
      red: 'bg-red-50 text-red-600 border-red-200'
    };

    return (
      <div className="bg-white rounded-xl shadow-agno border border-gray-100 p-6 hover:shadow-agno-lg transition-all duration-300">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className={`p-3 rounded-lg ${colorClasses[color]}`}>
              <Icon size={24} />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">{title}</p>
              <p className="text-2xl font-bold text-gray-900">{value}</p>
              {subtitle && (
                <p className="text-sm text-gray-500">{subtitle}</p>
              )}
            </div>
          </div>
          {trend && (
            <div className={`flex items-center space-x-1 ${trend === 'up' ? 'text-green-600' : 'text-red-600'}`}>
              {trend === 'up' ? <ArrowUp size={16} /> : <ArrowDown size={16} />}
              <span className="text-sm font-medium">{trendValue}%</span>
            </div>
          )}
        </div>
      </div>
    );
  };

  const DepartmentChart = () => (
    <div className="bg-white rounded-xl shadow-agno border border-gray-100 p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Department Distribution</h3>
        <button className="text-gray-400 hover:text-gray-600">
          <Eye size={20} />
        </button>
      </div>
      <div className="space-y-4">
        {analyticsData.departments.map((dept, index) => (
          <div key={index} className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className={`w-3 h-3 rounded-full bg-${['blue', 'green', 'orange', 'purple', 'red', 'yellow'][index % 6]}-500`}></div>
              <span className="text-sm font-medium text-gray-700">{dept.name}</span>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-500">{dept.count} employees</span>
              <span className="text-sm font-medium text-gray-900">{dept.percentage}%</span>
              <div className={`flex items-center space-x-1 ${dept.growth > 0 ? 'text-green-600' : 'text-red-600'}`}>
                {dept.growth > 0 ? <ArrowUp size={12} /> : <ArrowDown size={12} />}
                <span className="text-xs">{Math.abs(dept.growth)}%</span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const PerformanceChart = () => (
    <div className="bg-white rounded-xl shadow-agno border border-gray-100 p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Performance Distribution</h3>
        <div className="flex items-center space-x-2">
          <Star className="text-yellow-500" size={16} />
          <span className="text-sm font-medium text-gray-700">
            Avg: {analyticsData.performance_metrics.average_performance}/5.0
          </span>
        </div>
      </div>
      <div className="space-y-3">
        {analyticsData.performance_metrics.performance_distribution.map((range, index) => (
          <div key={index} className="flex items-center justify-between">
            <span className="text-sm text-gray-600">{range.range}</span>
            <div className="flex items-center space-x-3">
              <div className="w-32 bg-gray-200 rounded-full h-2">
                <div
                  className="bg-agno-primary h-2 rounded-full"
                  style={{ width: `${range.percentage}%` }}
                ></div>
              </div>
              <span className="text-sm font-medium text-gray-900 w-12 text-right">
                {range.count}
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const TopPerformers = () => (
    <div className="bg-white rounded-xl shadow-agno border border-gray-100 p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Top Performers</h3>
        <Award className="text-yellow-500" size={20} />
      </div>
      <div className="space-y-4">
        {analyticsData.performance_metrics.top_performers.map((performer, index) => (
          <div key={index} className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-br from-agno-primary to-agno-primary-dark rounded-full flex items-center justify-center text-white text-sm font-semibold">
                {index + 1}
              </div>
              <div>
                <p className="text-sm font-medium text-gray-900">{performer.name}</p>
                <p className="text-xs text-gray-500">{performer.department}</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Star className="text-yellow-400" size={14} />
              <span className="text-sm font-medium text-gray-900">{performer.score}</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const { overview } = analyticsData;

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Employee Analytics</h1>
          <p className="text-gray-600">Comprehensive insights and metrics for workforce management</p>
        </div>
        
        <div className="flex items-center space-x-3">
          <select
            value={selectedTimeframe}
            onChange={(e) => setSelectedTimeframe(e.target.value)}
            className="form-input text-sm"
          >
            <option value="week">This Week</option>
            <option value="month">This Month</option>
            <option value="quarter">This Quarter</option>
            <option value="year">This Year</option>
          </select>
          
          <button
            onClick={loadAnalyticsData}
            className="btn-secondary flex items-center space-x-2 px-4 py-2 rounded-lg"
          >
            <RefreshCw size={16} />
            <span>Refresh</span>
          </button>
          
          <button className="btn-agno-primary flex items-center space-x-2 px-4 py-2 rounded-lg">
            <Download size={16} />
            <span>Export Report</span>
          </button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="Total Employees"
          value={overview.total_employees}
          subtitle={`${overview.active_employees} active`}
          icon={Users}
          trend="up"
          trendValue={overview.growth_rate}
          color="blue"
        />
        <MetricCard
          title="New Hires"
          value={overview.new_hires}
          subtitle="This period"
          icon={UserPlus}
          trend="up"
          trendValue="15.2"
          color="green"
        />
        <MetricCard
          title="Retention Rate"
          value={`${overview.retention_rate}%`}
          subtitle="Last 12 months"
          icon={Target}
          trend="up"
          trendValue="2.1"
          color="purple"
        />
        <MetricCard
          title="Avg Performance"
          value={`${overview.satisfaction_score}/5`}
          subtitle="Employee rating"
          icon={Star}
          trend="up"
          trendValue="5.7"
          color="orange"
        />
      </div>

      {/* Charts and Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <DepartmentChart />
        <PerformanceChart />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <TopPerformers />
        
        {/* Additional Metrics */}
        <div className="bg-white rounded-xl shadow-agno border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Workforce Metrics</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Average Tenure</span>
              <span className="text-sm font-medium text-gray-900">{overview.average_tenure} years</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Departures</span>
              <span className="text-sm font-medium text-gray-900">{overview.departures}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Growth Rate</span>
              <span className="text-sm font-medium text-green-600">+{overview.growth_rate}%</span>
            </div>
          </div>
        </div>

        {/* Diversity Metrics */}
        <div className="bg-white rounded-xl shadow-agno border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Diversity Overview</h3>
          <div className="space-y-4">
            <div>
              <p className="text-sm text-gray-600 mb-2">Gender Distribution</p>
              {analyticsData.diversity_metrics.gender_distribution.map((item, index) => (
                <div key={index} className="flex justify-between items-center mb-1">
                  <span className="text-xs text-gray-500">{item.category}</span>
                  <span className="text-xs font-medium text-gray-900">{item.percentage}%</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmployeeAnalytics;
