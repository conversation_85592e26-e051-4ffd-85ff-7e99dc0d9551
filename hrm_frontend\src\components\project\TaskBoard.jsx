/**
 * Enhanced Kanban Task Board Component
 * Drag-and-drop task management with real-time updates
 * Styled consistently with Leave Management components
 */

import React, { useState, useEffect } from 'react';
import {
  Plus,
  Search,
  Filter,
  MoreVertical,
  Calendar,
  User,
  Clock,
  AlertCircle,
  CheckCircle,
  XCircle,
  Target,
  Flag,
  MessageSquare,
  Paperclip,
  Eye,
  Edit,
  Trash2
} from 'lucide-react';
import { usePermissions } from '../../hooks/usePermissions';
import { PermissionGate } from '../ProtectedRoute';
import { useAuth } from '../../contexts/AuthContext';
import { filterTasksByRole, canPerformAction } from '../../utils/roleBasedFiltering';
import apiService from '../../services/api';

const TaskBoard = ({ projectId, onCreateTask, onEditTask, onViewTask }) => {
  const [columns, setColumns] = useState([]);
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterAssignee, setFilterAssignee] = useState('all');
  const [filterPriority, setFilterPriority] = useState('all');
  const [draggedTask, setDraggedTask] = useState(null);
  const permissions = usePermissions();
  const { user, userRole } = useAuth();

  // Default kanban columns
  const defaultColumns = [
    { id: 'todo', title: 'To Do', color: 'bg-gray-100', limit: null },
    { id: 'in-progress', title: 'In Progress', color: 'bg-blue-100', limit: 3 },
    { id: 'review', title: 'Review', color: 'bg-yellow-100', limit: 2 },
    { id: 'done', title: 'Done', color: 'bg-green-100', limit: null }
  ];

  useEffect(() => {
    loadTasks();
  }, [projectId, filterAssignee, filterPriority]);

  const loadTasks = async () => {
    try {
      setLoading(true);
      const params = {};
      if (projectId) params.project_id = projectId;
      if (filterAssignee !== 'all') params.assignee_id = filterAssignee;
      if (filterPriority !== 'all') params.priority = filterPriority;
      if (searchTerm) params.search = searchTerm;

      const data = await apiService.getProjectTasks(projectId, params);
      setTasks(data.tasks || []);
      setColumns(defaultColumns);
    } catch (error) {
      console.error('Error loading tasks:', error);
      setTasks([]);
      setColumns(defaultColumns);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
    // Debounce search
    clearTimeout(window.taskSearchTimeout);
    window.taskSearchTimeout = setTimeout(() => {
      loadTasks();
    }, 500);
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return 'border-l-red-500 bg-red-50';
      case 'medium': return 'border-l-yellow-500 bg-yellow-50';
      case 'low': return 'border-l-green-500 bg-green-50';
      default: return 'border-l-gray-500 bg-gray-50';
    }
  };

  const getPriorityIcon = (priority) => {
    switch (priority) {
      case 'high': return <Flag size={14} className="text-red-500" />;
      case 'medium': return <Flag size={14} className="text-yellow-500" />;
      case 'low': return <Flag size={14} className="text-green-500" />;
      default: return <Flag size={14} className="text-gray-500" />;
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'done': return <CheckCircle size={16} className="text-green-600" />;
      case 'in-progress': return <Clock size={16} className="text-blue-600" />;
      case 'review': return <Eye size={16} className="text-yellow-600" />;
      default: return <Target size={16} className="text-gray-600" />;
    }
  };

  const handleDragStart = (e, task) => {
    setDraggedTask(task);
    e.dataTransfer.effectAllowed = 'move';
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    e.dataTransfer.dropEffect = 'move';
  };

  const handleDrop = async (e, columnId) => {
    e.preventDefault();
    
    if (!draggedTask || draggedTask.status === columnId) {
      setDraggedTask(null);
      return;
    }

    try {
      // Update task status
      await apiService.updateTask(draggedTask.id, { status: columnId });
      
      // Update local state
      setTasks(prev => prev.map(task => 
        task.id === draggedTask.id 
          ? { ...task, status: columnId }
          : task
      ));
      
      setDraggedTask(null);
    } catch (error) {
      console.error('Error updating task status:', error);
      setDraggedTask(null);
    }
  };

  // Apply role-based filtering first
  const roleFilteredTasks = filterTasksByRole(
    tasks,
    userRole,
    user?.id,
    user?.department,
    user?.team_ids || []
  );

  const getTasksForColumn = (columnId) => {
    return roleFilteredTasks.filter(task => task.status === columnId);
  };

  const filteredTasks = roleFilteredTasks.filter(task => {
    const matchesSearch = !searchTerm ||
      task.title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      task.description?.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesSearch;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-agno-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header and Controls */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Task Board</h2>
          <p className="text-gray-600 mt-1">Manage tasks with drag-and-drop kanban board</p>
        </div>
        
        <PermissionGate permission="taskManagement">
          <button
            onClick={() => onCreateTask?.()}
            className="flex items-center px-4 py-2 agno-bg-primary text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus size={16} className="mr-2" />
            Add Task
          </button>
        </PermissionGate>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
          <input
            type="text"
            placeholder="Search tasks..."
            value={searchTerm}
            onChange={handleSearch}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        
        <div className="flex gap-3">
          <select
            value={filterAssignee}
            onChange={(e) => setFilterAssignee(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">All Assignees</option>
            <option value="me">Assigned to Me</option>
            <option value="unassigned">Unassigned</option>
          </select>
          
          <select
            value={filterPriority}
            onChange={(e) => setFilterPriority(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">All Priority</option>
            <option value="high">High</option>
            <option value="medium">Medium</option>
            <option value="low">Low</option>
          </select>
          
          <button className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
            <Filter size={16} />
          </button>
        </div>
      </div>

      {/* Kanban Board */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {columns.map((column) => {
          const columnTasks = getTasksForColumn(column.id);
          const isOverLimit = column.limit && columnTasks.length > column.limit;
          
          return (
            <div
              key={column.id}
              className={`${column.color} rounded-lg p-4 min-h-[500px]`}
              onDragOver={handleDragOver}
              onDrop={(e) => handleDrop(e, column.id)}
            >
              {/* Column Header */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-2">
                  <h3 className="font-semibold text-gray-900">{column.title}</h3>
                  {getStatusIcon(column.id)}
                </div>
                <div className="flex items-center space-x-2">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                    isOverLimit ? 'bg-red-100 text-red-600' : 'bg-gray-200 text-gray-600'
                  }`}>
                    {columnTasks.length}
                    {column.limit && ` / ${column.limit}`}
                  </span>
                  <PermissionGate permission="taskManagement">
                    <button
                      onClick={() => onCreateTask?.(column.id)}
                      className="text-gray-400 hover:text-gray-600 transition-colors"
                    >
                      <Plus size={16} />
                    </button>
                  </PermissionGate>
                </div>
              </div>

              {/* Tasks */}
              <div className="space-y-3">
                {columnTasks.map((task) => (
                  <div
                    key={task.id}
                    draggable
                    onDragStart={(e) => handleDragStart(e, task)}
                    className={`bg-white rounded-lg p-4 shadow-sm border-l-4 cursor-move hover:shadow-md transition-shadow ${getPriorityColor(task.priority)}`}
                  >
                    {/* Task Header */}
                    <div className="flex items-start justify-between mb-2">
                      <h4 className="font-medium text-gray-900 text-sm line-clamp-2">
                        {task.title || 'Untitled Task'}
                      </h4>
                      <div className="flex items-center space-x-1 ml-2">
                        {getPriorityIcon(task.priority)}
                        <button className="text-gray-400 hover:text-gray-600 transition-colors">
                          <MoreVertical size={14} />
                        </button>
                      </div>
                    </div>

                    {/* Task Description */}
                    {task.description && (
                      <p className="text-xs text-gray-600 mb-3 line-clamp-2">
                        {task.description}
                      </p>
                    )}

                    {/* Task Meta */}
                    <div className="space-y-2">
                      {/* Due Date */}
                      {task.due_date && (
                        <div className="flex items-center space-x-2">
                          <Calendar size={12} className="text-gray-400" />
                          <span className="text-xs text-gray-500">
                            {new Date(task.due_date).toLocaleDateString()}
                          </span>
                        </div>
                      )}

                      {/* Assignee */}
                      {task.assignee && (
                        <div className="flex items-center space-x-2">
                          <User size={12} className="text-gray-400" />
                          <span className="text-xs text-gray-600">
                            {task.assignee.first_name} {task.assignee.last_name}
                          </span>
                        </div>
                      )}

                      {/* Task Stats */}
                      <div className="flex items-center justify-between pt-2">
                        <div className="flex items-center space-x-3">
                          {task.comments_count > 0 && (
                            <div className="flex items-center space-x-1">
                              <MessageSquare size={12} className="text-gray-400" />
                              <span className="text-xs text-gray-500">{task.comments_count}</span>
                            </div>
                          )}
                          {task.attachments_count > 0 && (
                            <div className="flex items-center space-x-1">
                              <Paperclip size={12} className="text-gray-400" />
                              <span className="text-xs text-gray-500">{task.attachments_count}</span>
                            </div>
                          )}
                        </div>

                        <div className="flex items-center space-x-1">
                          {canPerformAction('view', 'task', task, userRole, user?.id) && (
                            <button
                              onClick={() => onViewTask?.(task)}
                              className="text-gray-400 hover:text-blue-600 transition-colors"
                            >
                              <Eye size={12} />
                            </button>
                          )}
                          {canPerformAction('edit', 'task', task, userRole, user?.id) && (
                            <PermissionGate permission="taskManagement">
                              <button
                                onClick={() => onEditTask?.(task)}
                                className="text-gray-400 hover:text-green-600 transition-colors"
                              >
                                <Edit size={12} />
                              </button>
                            </PermissionGate>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}

                {/* Empty State */}
                {columnTasks.length === 0 && (
                  <div className="text-center py-8">
                    <Target size={32} className="mx-auto text-gray-300 mb-2" />
                    <p className="text-gray-500 text-sm">No tasks</p>
                    <PermissionGate permission="taskManagement">
                      <button
                        onClick={() => onCreateTask?.(column.id)}
                        className="mt-2 text-blue-600 hover:text-blue-800 text-xs font-medium"
                      >
                        Add task
                      </button>
                    </PermissionGate>
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {/* Board Stats */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Board Statistics</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <p className="text-2xl font-bold text-blue-600">{getTasksForColumn('todo').length}</p>
            <p className="text-sm text-gray-600">To Do</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-yellow-600">{getTasksForColumn('in-progress').length}</p>
            <p className="text-sm text-gray-600">In Progress</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-orange-600">{getTasksForColumn('review').length}</p>
            <p className="text-sm text-gray-600">Review</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-green-600">{getTasksForColumn('done').length}</p>
            <p className="text-sm text-gray-600">Done</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TaskBoard;
