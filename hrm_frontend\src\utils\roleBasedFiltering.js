/**
 * Role-based Content Filtering Utilities
 * Filters projects, tasks, and other content based on user roles and permissions
 */

import { ROLES, PERMISSION_TYPES } from '../services/permissions';

/**
 * Filter projects based on user role and permissions
 * @param {Array} projects - Array of project objects
 * @param {string} userRole - Current user's role
 * @param {string} userId - Current user's ID
 * @param {string} userDepartment - Current user's department
 * @param {Array} userTeamIds - Array of team IDs the user belongs to
 * @returns {Array} Filtered projects array
 */
export const filterProjectsByRole = (projects, userRole, userId, userDepartment = null, userTeamIds = []) => {
  if (!projects || !Array.isArray(projects)) return [];

  switch (userRole) {
    case ROLES.SUPER_ADMIN:
      // SuperAdmin sees all projects
      return projects;

    case ROLES.ADMIN:
      // Admin sees all projects (if they have permission)
      return projects;

    case ROLES.HR:
      // HR sees all projects (if they have permission)
      return projects;

    case ROLES.MANAGER:
      // Manager sees only team projects and projects they manage
      return projects.filter(project => {
        return (
          project.project_manager_id === userId ||
          project.department_id === userDepartment ||
          (project.team_ids && project.team_ids.some(teamId => userTeamIds.includes(teamId))) ||
          (project.team_members && project.team_members.some(member => 
            member.department === userDepartment || userTeamIds.includes(member.team_id)
          ))
        );
      });

    case ROLES.EMPLOYEE:
      // Employee sees only assigned projects
      return projects.filter(project => {
        return (
          project.assignedTo === userId ||
          project.createdBy === userId ||
          (project.team_members && project.team_members.some(member => member.id === userId)) ||
          (project.assignedUsers && project.assignedUsers.includes(userId))
        );
      });

    default:
      return [];
  }
};

/**
 * Filter tasks based on user role and permissions
 * @param {Array} tasks - Array of task objects
 * @param {string} userRole - Current user's role
 * @param {string} userId - Current user's ID
 * @param {string} userDepartment - Current user's department
 * @param {Array} userTeamIds - Array of team IDs the user belongs to
 * @returns {Array} Filtered tasks array
 */
export const filterTasksByRole = (tasks, userRole, userId, userDepartment = null, userTeamIds = []) => {
  if (!tasks || !Array.isArray(tasks)) return [];

  switch (userRole) {
    case ROLES.SUPER_ADMIN:
      // SuperAdmin sees all tasks
      return tasks;

    case ROLES.ADMIN:
      // Admin sees all tasks (if they have permission)
      return tasks;

    case ROLES.HR:
      // HR sees all tasks (if they have permission)
      return tasks;

    case ROLES.MANAGER:
      // Manager sees team tasks and tasks they created/manage
      return tasks.filter(task => {
        return (
          task.created_by === userId ||
          task.assignee_id === userId ||
          (task.assignee && task.assignee.department === userDepartment) ||
          (task.project && task.project.project_manager_id === userId) ||
          (task.project && task.project.department_id === userDepartment) ||
          (task.watchers && task.watchers.includes(userId))
        );
      });

    case ROLES.EMPLOYEE:
      // Employee sees only assigned tasks or tasks they created
      return tasks.filter(task => {
        return (
          task.assignee_id === userId ||
          task.created_by === userId ||
          (task.watchers && task.watchers.includes(userId)) ||
          (task.collaborators && task.collaborators.includes(userId))
        );
      });

    default:
      return [];
  }
};

/**
 * Filter team members based on user role and permissions
 * @param {Array} members - Array of team member objects
 * @param {string} userRole - Current user's role
 * @param {string} userId - Current user's ID
 * @param {string} userDepartment - Current user's department
 * @returns {Array} Filtered team members array
 */
export const filterTeamMembersByRole = (members, userRole, userId, userDepartment = null) => {
  if (!members || !Array.isArray(members)) return [];

  switch (userRole) {
    case ROLES.SUPER_ADMIN:
    case ROLES.ADMIN:
    case ROLES.HR:
      // Admin roles see all team members
      return members;

    case ROLES.MANAGER:
      // Manager sees team members in their department
      return members.filter(member => 
        member.department === userDepartment || member.manager_id === userId
      );

    case ROLES.EMPLOYEE:
      // Employee sees team members they work with
      return members.filter(member => 
        member.department === userDepartment || member.id === userId
      );

    default:
      return [];
  }
};

/**
 * Filter analytics data based on user role and permissions
 * @param {Object} analyticsData - Analytics data object
 * @param {string} userRole - Current user's role
 * @param {string} userId - Current user's ID
 * @param {string} userDepartment - Current user's department
 * @returns {Object} Filtered analytics data
 */
export const filterAnalyticsByRole = (analyticsData, userRole, userId, userDepartment = null) => {
  if (!analyticsData) return null;

  switch (userRole) {
    case ROLES.SUPER_ADMIN:
      // SuperAdmin sees all analytics
      return analyticsData;

    case ROLES.ADMIN:
    case ROLES.HR:
      // Admin/HR sees organization-wide analytics
      return analyticsData;

    case ROLES.MANAGER:
      // Manager sees team analytics only
      return {
        ...analyticsData,
        team_performance: analyticsData.team_performance?.filter(member => 
          member.department === userDepartment
        ),
        // Filter other analytics to team scope
        overview: {
          ...analyticsData.overview,
          // Adjust metrics to team scope if needed
        }
      };

    case ROLES.EMPLOYEE:
      // Employee sees limited analytics (personal performance)
      return {
        overview: {
          total_projects: analyticsData.overview?.total_projects || 0,
          active_projects: analyticsData.overview?.active_projects || 0,
          my_tasks: analyticsData.overview?.my_tasks || 0,
          completed_tasks: analyticsData.overview?.my_completed_tasks || 0
        },
        team_performance: analyticsData.team_performance?.filter(member => 
          member.id === userId
        ),
        // Remove sensitive analytics
        time_tracking: {
          my_hours_logged: analyticsData.time_tracking?.my_hours_logged || 0,
          my_efficiency: analyticsData.time_tracking?.my_efficiency || 0
        }
      };

    default:
      return null;
  }
};

/**
 * Check if user can perform action on resource
 * @param {string} action - Action to perform (create, edit, delete, view)
 * @param {string} resourceType - Type of resource (project, task, etc.)
 * @param {Object} resource - Resource object
 * @param {string} userRole - Current user's role
 * @param {string} userId - Current user's ID
 * @returns {boolean} Whether user can perform the action
 */
export const canPerformAction = (action, resourceType, resource, userRole, userId) => {
  switch (userRole) {
    case ROLES.SUPER_ADMIN:
      // SuperAdmin can do everything
      return true;

    case ROLES.ADMIN:
    case ROLES.HR:
      // Admin/HR can do most things
      return ['view', 'create', 'edit'].includes(action);

    case ROLES.MANAGER:
      // Manager can manage team resources
      if (action === 'view') return true;
      if (action === 'create') return true;
      if (action === 'edit') {
        return (
          resource.project_manager_id === userId ||
          resource.created_by === userId ||
          resource.assignee_id === userId
        );
      }
      if (action === 'delete') {
        return resource.created_by === userId;
      }
      return false;

    case ROLES.EMPLOYEE:
      // Employee can only manage assigned resources
      if (action === 'view') {
        return (
          resource.assignee_id === userId ||
          resource.created_by === userId ||
          (resource.assignedUsers && resource.assignedUsers.includes(userId))
        );
      }
      if (action === 'create') return resourceType === 'task'; // Can create tasks
      if (action === 'edit') {
        return (
          resource.assignee_id === userId ||
          resource.created_by === userId
        );
      }
      if (action === 'delete') {
        return resource.created_by === userId;
      }
      return false;

    default:
      return false;
  }
};

/**
 * Get available actions for a resource based on user role
 * @param {string} resourceType - Type of resource (project, task, etc.)
 * @param {Object} resource - Resource object
 * @param {string} userRole - Current user's role
 * @param {string} userId - Current user's ID
 * @returns {Array} Array of available actions
 */
export const getAvailableActions = (resourceType, resource, userRole, userId) => {
  const allActions = ['view', 'create', 'edit', 'delete', 'assign', 'comment'];
  
  return allActions.filter(action => 
    canPerformAction(action, resourceType, resource, userRole, userId)
  );
};

/**
 * Filter navigation items based on user role
 * @param {Array} navigationItems - Array of navigation items
 * @param {string} userRole - Current user's role
 * @returns {Array} Filtered navigation items
 */
export const filterNavigationByRole = (navigationItems, userRole) => {
  if (!navigationItems || !Array.isArray(navigationItems)) return [];

  switch (userRole) {
    case ROLES.SUPER_ADMIN:
      // SuperAdmin sees specific items only
      return navigationItems.filter(item =>
        ['dashboard', 'onboarding', 'leave', 'timesheet-approval', 'projects', 'tasks'].includes(item.id)
      );

    case ROLES.ADMIN:
    case ROLES.HR:
    case ROLES.MANAGER:
    case ROLES.EMPLOYEE:
      // Other roles see all items (filtered by permissions)
      return navigationItems;

    default:
      return [];
  }
};
