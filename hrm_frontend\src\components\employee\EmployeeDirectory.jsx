/**
 * Enhanced Employee Directory Component
 * Features: Advanced search, filtering, premium card designs, list/grid views
 * Professional UI/UX with modern design patterns
 */

import React, { useState, useEffect } from 'react';
import {
  Search,
  Filter,
  Grid3X3,
  List,
  Plus,
  Eye,
  Edit,
  MoreVertical,
  MapPin,
  Mail,
  Phone,
  Briefcase,
  Calendar,
  Star,
  Users,
  Building,
  Clock,
  ChevronDown,
  X,
  Download,
  Upload
} from 'lucide-react';
import { usePermissions } from '../../hooks/usePermissions';
import { PermissionGate } from '../ProtectedRoute';
import apiService from '../../services/api';

const EmployeeDirectory = () => {
  const [employees, setEmployees] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDepartment, setSelectedDepartment] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [viewMode, setViewMode] = useState('grid'); // 'grid' or 'list'
  const [showFilters, setShowFilters] = useState(false);
  const [sortBy, setSortBy] = useState('name');
  const [sortOrder, setSortOrder] = useState('asc');
  const permissions = usePermissions();

  // Mock departments for filtering
  const departments = [
    'Engineering',
    'Sales',
    'Marketing',
    'HR',
    'Finance',
    'Operations',
    'Customer Support'
  ];

  useEffect(() => {
    fetchEmployees();
  }, [searchTerm, selectedDepartment, selectedStatus, sortBy, sortOrder]);

  const fetchEmployees = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await apiService.getEmployees({
        search: searchTerm,
        department: selectedDepartment !== 'all' ? selectedDepartment : undefined,
        status: selectedStatus !== 'all' ? selectedStatus : undefined,
        sort_by: sortBy,
        sort_order: sortOrder
      });

      // Handle different response formats
      if (response && response.employees) {
        setEmployees(response.employees);
      } else if (Array.isArray(response)) {
        setEmployees(response);
      } else {
        // Fallback to mock data
        setEmployees(getMockEmployees());
      }
    } catch (err) {
      console.error('Error fetching employees:', err);
      setError('Failed to load employees. Please try again.');
      setEmployees(getMockEmployees());
    } finally {
      setLoading(false);
    }
  };

  const getMockEmployees = () => [
    {
      id: 1,
      employee_id: 'EMP001',
      first_name: 'Sarah',
      last_name: 'Johnson',
      email: '<EMAIL>',
      phone: '+****************',
      department: 'Engineering',
      position: 'Senior Software Engineer',
      hire_date: '2022-03-15',
      is_active: true,
      location: 'San Francisco, CA',
      performance_rating: 4.8
    },
    {
      id: 2,
      employee_id: 'EMP002',
      first_name: 'Michael',
      last_name: 'Chen',
      email: '<EMAIL>',
      phone: '+****************',
      department: 'Marketing',
      position: 'Marketing Manager',
      hire_date: '2021-08-20',
      is_active: true,
      location: 'New York, NY',
      performance_rating: 4.6
    },
    {
      id: 3,
      employee_id: 'EMP003',
      first_name: 'Emily',
      last_name: 'Davis',
      email: '<EMAIL>',
      phone: '+****************',
      department: 'HR',
      position: 'HR Specialist',
      hire_date: '2023-01-10',
      is_active: true,
      location: 'Austin, TX',
      performance_rating: 4.7
    },
    {
      id: 4,
      employee_id: 'EMP004',
      first_name: 'David',
      last_name: 'Wilson',
      email: '<EMAIL>',
      phone: '+****************',
      department: 'Sales',
      position: 'Sales Representative',
      hire_date: '2022-11-05',
      is_active: true,
      location: 'Chicago, IL',
      performance_rating: 4.5
    },
    {
      id: 5,
      employee_id: 'EMP005',
      first_name: 'Jessica',
      last_name: 'Brown',
      email: '<EMAIL>',
      phone: '+****************',
      department: 'Finance',
      position: 'Financial Analyst',
      hire_date: '2021-06-12',
      is_active: true,
      location: 'Boston, MA',
      performance_rating: 4.9
    }
  ];

  // Filter employees based on search and filters
  const filteredEmployees = employees.filter(employee => {
    const fullName = `${employee.first_name || ''} ${employee.last_name || ''}`.toLowerCase();
    const matchesSearch = fullName.includes(searchTerm.toLowerCase()) ||
                         (employee.email || '').toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (employee.employee_id || '').toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesDepartment = selectedDepartment === 'all' || employee.department === selectedDepartment;
    const matchesStatus = selectedStatus === 'all' || 
                         (selectedStatus === 'active' && employee.is_active) ||
                         (selectedStatus === 'inactive' && !employee.is_active);

    return matchesSearch && matchesDepartment && matchesStatus;
  });

  const EmployeeCard = ({ employee }) => {
    const fullName = `${employee.first_name || ''} ${employee.last_name || ''}`.trim();
    const initials = fullName.split(' ').map(n => n[0]).join('').toUpperCase();
    
    return (
      <div className="bg-white rounded-xl shadow-agno border border-gray-100 p-6 hover:shadow-agno-lg transition-all duration-300 group">
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <div className="w-14 h-14 bg-gradient-to-br from-agno-primary to-agno-primary-dark rounded-xl flex items-center justify-center text-white font-semibold text-lg">
                {initials}
              </div>
              <div className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white ${
                employee.is_active ? 'bg-green-500' : 'bg-gray-400'
              }`}></div>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">{fullName}</h3>
              <p className="text-sm text-gray-600">{employee.position}</p>
              <p className="text-xs text-gray-500">{employee.employee_id}</p>
            </div>
          </div>
          
          <div className="opacity-0 group-hover:opacity-100 transition-opacity">
            <button className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100">
              <MoreVertical size={16} />
            </button>
          </div>
        </div>

        <div className="space-y-3">
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <Building size={14} />
            <span>{employee.department}</span>
          </div>
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <Mail size={14} />
            <span className="truncate">{employee.email}</span>
          </div>
          <div className="flex items-center space-x-2 text-sm text-gray-600">
            <Phone size={14} />
            <span>{employee.phone}</span>
          </div>
          {employee.location && (
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <MapPin size={14} />
              <span>{employee.location}</span>
            </div>
          )}
        </div>

        <div className="mt-4 pt-4 border-t border-gray-100 flex items-center justify-between">
          <div className="flex items-center space-x-1">
            <Star className="text-yellow-400" size={14} />
            <span className="text-sm font-medium text-gray-700">
              {employee.performance_rating || 'N/A'}
            </span>
          </div>
          
          <div className="flex items-center space-x-2">
            <PermissionGate permission="employeeDirectory">
              <button className="p-2 text-gray-400 hover:text-blue-600 rounded-lg hover:bg-blue-50 transition-colors">
                <Eye size={14} />
              </button>
              <button className="p-2 text-gray-400 hover:text-green-600 rounded-lg hover:bg-green-50 transition-colors">
                <Edit size={14} />
              </button>
            </PermissionGate>
          </div>
        </div>
      </div>
    );
  };

  const EmployeeListItem = ({ employee }) => {
    const fullName = `${employee.first_name || ''} ${employee.last_name || ''}`.trim();
    const initials = fullName.split(' ').map(n => n[0]).join('').toUpperCase();
    
    return (
      <div className="bg-white rounded-lg shadow-agno border border-gray-100 p-4 hover:shadow-agno-lg transition-all duration-300 group">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <div className="w-12 h-12 bg-gradient-to-br from-agno-primary to-agno-primary-dark rounded-lg flex items-center justify-center text-white font-semibold">
                {initials}
              </div>
              <div className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-white ${
                employee.is_active ? 'bg-green-500' : 'bg-gray-400'
              }`}></div>
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-3">
                <h3 className="text-lg font-semibold text-gray-900">{fullName}</h3>
                <span className="text-xs text-gray-500">{employee.employee_id}</span>
              </div>
              <p className="text-sm text-gray-600">{employee.position}</p>
              <div className="flex items-center space-x-4 mt-1">
                <span className="text-xs text-gray-500 flex items-center space-x-1">
                  <Building size={12} />
                  <span>{employee.department}</span>
                </span>
                <span className="text-xs text-gray-500 flex items-center space-x-1">
                  <Mail size={12} />
                  <span>{employee.email}</span>
                </span>
                {employee.location && (
                  <span className="text-xs text-gray-500 flex items-center space-x-1">
                    <MapPin size={12} />
                    <span>{employee.location}</span>
                  </span>
                )}
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-1">
              <Star className="text-yellow-400" size={14} />
              <span className="text-sm font-medium text-gray-700">
                {employee.performance_rating || 'N/A'}
              </span>
            </div>
            
            <div className="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity">
              <PermissionGate permission="employeeDirectory">
                <button className="p-2 text-gray-400 hover:text-blue-600 rounded-lg hover:bg-blue-50 transition-colors">
                  <Eye size={14} />
                </button>
                <button className="p-2 text-gray-400 hover:text-green-600 rounded-lg hover:bg-green-50 transition-colors">
                  <Edit size={14} />
                </button>
              </PermissionGate>
              <button className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors">
                <MoreVertical size={14} />
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Employee Directory</h1>
          <p className="text-gray-600">Manage and view all employee information</p>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-3">
          <button className="btn-secondary flex items-center space-x-2 px-4 py-2 rounded-lg">
            <Download size={16} />
            <span>Export</span>
          </button>
          <PermissionGate permission="employeeDirectory">
            <button className="btn-agno-primary flex items-center space-x-2 px-4 py-2 rounded-lg">
              <Plus size={16} />
              <span>Add Employee</span>
            </button>
          </PermissionGate>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-xl shadow-agno border border-gray-100 p-6">
        <div className="flex flex-col lg:flex-row gap-4">
          {/* Search */}
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
              <input
                type="text"
                placeholder="Search employees by name, email, or ID..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-agno-primary focus:border-transparent"
              />
            </div>
          </div>

          {/* Filters */}
          <div className="flex items-center space-x-3">
            <select
              value={selectedDepartment}
              onChange={(e) => setSelectedDepartment(e.target.value)}
              className="form-input"
            >
              <option value="all">All Departments</option>
              {departments.map(dept => (
                <option key={dept} value={dept}>{dept}</option>
              ))}
            </select>

            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="form-input"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>

            <div className="flex items-center space-x-2 border-l border-gray-200 pl-3">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded-lg transition-colors ${
                  viewMode === 'grid' ? 'bg-agno-primary text-white' : 'text-gray-400 hover:text-gray-600'
                }`}
              >
                <Grid3X3 size={16} />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded-lg transition-colors ${
                  viewMode === 'list' ? 'bg-agno-primary text-white' : 'text-gray-400 hover:text-gray-600'
                }`}
              >
                <List size={16} />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Results Summary */}
      <div className="flex items-center justify-between">
        <p className="text-sm text-gray-600">
          Showing {filteredEmployees.length} of {employees.length} employees
        </p>
        <select
          value={`${sortBy}-${sortOrder}`}
          onChange={(e) => {
            const [field, order] = e.target.value.split('-');
            setSortBy(field);
            setSortOrder(order);
          }}
          className="text-sm border border-gray-200 rounded-lg px-3 py-2"
        >
          <option value="name-asc">Name (A-Z)</option>
          <option value="name-desc">Name (Z-A)</option>
          <option value="department-asc">Department (A-Z)</option>
          <option value="hire_date-desc">Newest First</option>
          <option value="hire_date-asc">Oldest First</option>
        </select>
      </div>

      {/* Employee Grid/List */}
      {loading ? (
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-agno-primary"></div>
        </div>
      ) : error ? (
        <div className="text-center py-12">
          <p className="text-red-600 mb-4">{error}</p>
          <button
            onClick={fetchEmployees}
            className="btn-agno-primary px-4 py-2 rounded-lg"
          >
            Retry
          </button>
        </div>
      ) : (
        <div className={
          viewMode === 'grid' 
            ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
            : "space-y-4"
        }>
          {filteredEmployees.map(employee => (
            viewMode === 'grid' 
              ? <EmployeeCard key={employee.id} employee={employee} />
              : <EmployeeListItem key={employee.id} employee={employee} />
          ))}
        </div>
      )}

      {filteredEmployees.length === 0 && !loading && !error && (
        <div className="text-center py-12">
          <Users className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <p className="text-gray-500 mb-2">No employees found</p>
          <p className="text-sm text-gray-400">Try adjusting your search criteria or filters</p>
        </div>
      )}
    </div>
  );
};

export default EmployeeDirectory;
