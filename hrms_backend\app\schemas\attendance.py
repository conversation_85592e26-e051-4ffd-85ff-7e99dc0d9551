from pydantic import BaseModel, <PERSON>, validator
from typing import Optional, List
from uuid import UUID
from datetime import date, datetime, time
from decimal import Decimal
from enum import Enum


class AttendanceStatus(str, Enum):
    PRESENT = "present"
    ABSENT = "absent"
    LATE = "late"
    HALF_DAY = "half_day"
    WORK_FROM_HOME = "work_from_home"
    ON_LEAVE = "on_leave"
    HOLIDAY = "holiday"


class CheckType(str, Enum):
    CHECK_IN = "check_in"
    CHECK_OUT = "check_out"
    BREAK_START = "break_start"
    BREAK_END = "break_end"


# Attendance Record Schemas
class AttendanceRecordBase(BaseModel):
    date: date
    status: AttendanceStatus = AttendanceStatus.PRESENT
    work_location: Optional[str] = Field(None, max_length=255)
    is_remote: bool = False


class AttendanceRecordCreate(AttendanceRecordBase):
    employee_id: UUID
    shift_id: Optional[UUID] = None


class AttendanceRecordUpdate(BaseModel):
    status: Optional[AttendanceStatus] = None
    work_location: Optional[str] = Field(None, max_length=255)
    is_remote: Optional[bool] = None
    notes: Optional[str] = None


class AttendanceRecordResponse(AttendanceRecordBase):
    id: UUID
    employee_id: UUID
    check_in_time: Optional[datetime] = None
    check_out_time: Optional[datetime] = None
    break_start_time: Optional[datetime] = None
    break_end_time: Optional[datetime] = None
    total_break_duration: Optional[int] = None  # in minutes
    total_hours_worked: Optional[Decimal] = None
    overtime_hours: Optional[Decimal] = None
    shift_id: Optional[UUID] = None
    expected_check_in: Optional[datetime] = None
    expected_check_out: Optional[datetime] = None
    is_approved: bool = False
    approved_by: Optional[UUID] = None
    approved_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class AttendanceListResponse(BaseModel):
    records: List[AttendanceRecordResponse]
    total: int
    skip: int
    limit: int


# Attendance Log Schemas
class AttendanceLogCreate(BaseModel):
    check_type: CheckType
    latitude: Optional[Decimal] = Field(None, ge=-90, le=90)
    longitude: Optional[Decimal] = Field(None, ge=-180, le=180)
    location_name: Optional[str] = Field(None, max_length=255)
    photo_url: Optional[str] = Field(None, max_length=500)


class AttendanceLogResponse(BaseModel):
    id: UUID
    employee_id: UUID
    attendance_record_id: Optional[UUID] = None
    timestamp: datetime
    check_type: CheckType
    latitude: Optional[Decimal] = None
    longitude: Optional[Decimal] = None
    location_name: Optional[str] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    photo_url: Optional[str] = None
    created_at: datetime

    class Config:
        from_attributes = True


# Check-in/out Request Schemas
class CheckInRequest(BaseModel):
    latitude: Optional[Decimal] = Field(None, ge=-90, le=90)
    longitude: Optional[Decimal] = Field(None, ge=-180, le=180)
    location_name: Optional[str] = Field(None, max_length=255)
    work_location: Optional[str] = Field(None, max_length=255)
    is_remote: bool = False
    photo_url: Optional[str] = Field(None, max_length=500)
    notes: Optional[str] = None


class CheckOutRequest(BaseModel):
    latitude: Optional[Decimal] = Field(None, ge=-90, le=90)
    longitude: Optional[Decimal] = Field(None, ge=-180, le=180)
    location_name: Optional[str] = Field(None, max_length=255)
    photo_url: Optional[str] = Field(None, max_length=500)
    notes: Optional[str] = None


class BreakRequest(BaseModel):
    break_type: CheckType  # break_start or break_end
    latitude: Optional[Decimal] = Field(None, ge=-90, le=90)
    longitude: Optional[Decimal] = Field(None, ge=-180, le=180)
    location_name: Optional[str] = Field(None, max_length=255)


# Attendance Policy Schemas
class AttendancePolicyBase(BaseModel):
    name: str = Field(..., min_length=1, max_length=200)
    standard_hours_per_day: Decimal = Field(8.0, ge=0, le=24)
    standard_hours_per_week: Decimal = Field(40.0, ge=0, le=168)
    late_grace_period: int = Field(15, ge=0)  # minutes
    early_departure_grace_period: int = Field(15, ge=0)  # minutes
    break_duration: int = Field(60, ge=0)  # minutes
    max_break_duration: int = Field(120, ge=0)  # minutes
    overtime_threshold: Decimal = Field(8.0, ge=0)  # hours
    overtime_multiplier: Decimal = Field(1.5, ge=1.0)


class AttendancePolicyCreate(AttendancePolicyBase):
    enable_geofencing: bool = False
    office_latitude: Optional[Decimal] = Field(None, ge=-90, le=90)
    office_longitude: Optional[Decimal] = Field(None, ge=-180, le=180)
    geofence_radius: Optional[int] = Field(100, ge=0)  # meters
    require_photo_checkin: bool = False
    require_photo_checkout: bool = False
    auto_clock_out_enabled: bool = True
    auto_clock_out_time: Optional[time] = None


class AttendancePolicyUpdate(BaseModel):
    name: Optional[str] = Field(None, min_length=1, max_length=200)
    standard_hours_per_day: Optional[Decimal] = Field(None, ge=0, le=24)
    standard_hours_per_week: Optional[Decimal] = Field(None, ge=0, le=168)
    late_grace_period: Optional[int] = Field(None, ge=0)
    early_departure_grace_period: Optional[int] = Field(None, ge=0)
    break_duration: Optional[int] = Field(None, ge=0)
    max_break_duration: Optional[int] = Field(None, ge=0)
    overtime_threshold: Optional[Decimal] = Field(None, ge=0)
    overtime_multiplier: Optional[Decimal] = Field(None, ge=1.0)
    enable_geofencing: Optional[bool] = None
    office_latitude: Optional[Decimal] = Field(None, ge=-90, le=90)
    office_longitude: Optional[Decimal] = Field(None, ge=-180, le=180)
    geofence_radius: Optional[int] = Field(None, ge=0)
    require_photo_checkin: Optional[bool] = None
    require_photo_checkout: Optional[bool] = None
    auto_clock_out_enabled: Optional[bool] = None
    auto_clock_out_time: Optional[time] = None


class AttendancePolicyResponse(AttendancePolicyBase):
    id: UUID
    organization_id: UUID
    enable_geofencing: bool
    office_latitude: Optional[Decimal] = None
    office_longitude: Optional[Decimal] = None
    geofence_radius: Optional[int] = None
    require_photo_checkin: bool
    require_photo_checkout: bool
    auto_clock_out_enabled: bool
    auto_clock_out_time: Optional[time] = None
    is_default: bool
    created_at: datetime
    updated_at: datetime
    is_active: bool

    class Config:
        from_attributes = True


# Attendance Summary Schemas
class AttendanceSummary(BaseModel):
    employee_id: UUID
    period_start: date
    period_end: date
    total_days: int
    present_days: int
    absent_days: int
    late_days: int
    half_days: int
    work_from_home_days: int
    total_hours_worked: Decimal
    total_overtime_hours: Decimal
    average_hours_per_day: Decimal


class AttendanceReport(BaseModel):
    period_start: date
    period_end: date
    summaries: List[AttendanceSummary]
    total_employees: int


# Real-time Attendance Status
class AttendanceStatusResponse(BaseModel):
    employee_id: UUID
    current_status: str  # checked_in, checked_out, on_break
    is_checked_in: bool = False
    is_on_break: bool = False
    check_in_time: Optional[datetime] = None
    check_out_time: Optional[datetime] = None
    break_start_time: Optional[datetime] = None
    break_end_time: Optional[datetime] = None
    total_hours_today: Optional[Decimal] = None
    last_action: Optional[CheckType] = None
    last_action_time: Optional[datetime] = None


# Bulk Operations
class BulkAttendanceUpdate(BaseModel):
    employee_ids: List[UUID]
    date: date
    status: AttendanceStatus
    notes: Optional[str] = None


class AttendanceApprovalRequest(BaseModel):
    attendance_record_ids: List[UUID]
    approved: bool
    comments: Optional[str] = None
