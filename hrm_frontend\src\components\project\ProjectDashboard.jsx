/**
 * Professional Project Management Dashboard
 * Features: Analytics, quick stats, recent activity, project overview
 * Styled consistently with Leave Management Dashboard
 */

import React, { useState, useEffect } from 'react';
import {
  Calendar,
  Clock,
  TrendingUp,
  Users,
  CheckCircle,
  XCircle,
  AlertCircle,
  Plus,
  Filter,
  Download,
  BarChart3,
  PieChart,
  Target,
  Briefcase,
  Activity,
  Award,
  ArrowUp,
  ArrowDown,
  Eye,
  Edit,
  MoreVertical
} from 'lucide-react';
import { usePermissions } from '../../hooks/usePermissions';
import { PermissionGate } from '../ProtectedRoute';
import { useProjectDashboard, useProjects } from '../../hooks/useProjectData';
import apiService from '../../services/api';

const ProjectDashboard = () => {
  const [selectedView, setSelectedView] = useState('overview');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const permissions = usePermissions();

  // Use custom hooks for data management
  const { dashboardData, loading, error, refreshDashboard } = useProjectDashboard();
  const { projects } = useProjects();

  // Fallback to mock data if API is not available
  const mockDashboard = {
    summary: {
      total_projects: projects?.length || 12,
      active_projects: projects?.filter(p => p.status === 'in-progress')?.length || 8,
      completed_projects: projects?.filter(p => p.status === 'completed')?.length || 4,
      overdue_tasks: 5,
      team_members: 12,
      completion_rate: 78
    },
    recent_projects: projects?.slice(0, 5) || [],
    upcoming_deadlines: [
      { id: 1, project: 'HRMS Development', task: 'User Authentication', due_date: '2024-01-15', priority: 'high' },
      { id: 2, project: 'Mobile App', task: 'UI Design Review', due_date: '2024-01-18', priority: 'medium' }
    ],
    team_performance: {
      productivity_score: 85,
      tasks_completed_today: 23,
      average_completion_time: '2.5 days'
    }
  };

  const displayData = dashboardData || mockDashboard;

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-agno-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-red-600 mb-4">Error loading dashboard: {error}</p>
          <button
            onClick={refreshDashboard}
            className="px-4 py-2 agno-bg-primary text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  const { summary, recent_projects, upcoming_deadlines, team_performance } = displayData || {};

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-100';
      case 'in-progress': return 'text-blue-600 bg-blue-100';
      case 'planning': return 'text-yellow-600 bg-yellow-100';
      case 'on-hold': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return 'text-red-600 bg-red-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Project Management</h1>
          <p className="text-gray-600 mt-1">Monitor project progress and team performance</p>
        </div>
        
        <div className="flex items-center space-x-3">
          <PermissionGate permission="projectKanbanBoards">
            <button
              onClick={() => setShowCreateForm(true)}
              className="flex items-center px-4 py-2 agno-bg-primary text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Plus size={16} className="mr-2" />
              New Project
            </button>
          </PermissionGate>
          
          <button className="flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
            <Download size={16} className="mr-2" />
            Export
          </button>
        </div>
      </div>

      {/* Quick Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Total Projects */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Projects</p>
              <p className="text-2xl font-bold text-blue-600 mt-1">
                {summary?.total_projects || 0}
              </p>
              <div className="flex items-center mt-2">
                <ArrowUp size={14} className="text-green-500 mr-1" />
                <span className="text-sm text-green-600">+12% from last month</span>
              </div>
            </div>
            <div className="p-3 bg-blue-100 rounded-lg">
              <Briefcase size={24} className="text-blue-600" />
            </div>
          </div>
        </div>

        {/* Active Projects */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Active Projects</p>
              <p className="text-2xl font-bold text-green-600 mt-1">
                {summary?.active_projects || 0}
              </p>
              <div className="flex items-center mt-2">
                <Activity size={14} className="text-green-500 mr-1" />
                <span className="text-sm text-green-600">On track</span>
              </div>
            </div>
            <div className="p-3 bg-green-100 rounded-lg">
              <Target size={24} className="text-green-600" />
            </div>
          </div>
        </div>

        {/* Team Members */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Team Members</p>
              <p className="text-2xl font-bold text-purple-600 mt-1">
                {summary?.team_members || 0}
              </p>
              <div className="flex items-center mt-2">
                <Users size={14} className="text-purple-500 mr-1" />
                <span className="text-sm text-purple-600">Active contributors</span>
              </div>
            </div>
            <div className="p-3 bg-purple-100 rounded-lg">
              <Users size={24} className="text-purple-600" />
            </div>
          </div>
        </div>

        {/* Completion Rate */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Completion Rate</p>
              <p className="text-2xl font-bold text-orange-600 mt-1">
                {summary?.completion_rate || 0}%
              </p>
              <div className="flex items-center mt-2">
                <Award size={14} className="text-orange-500 mr-1" />
                <span className="text-sm text-orange-600">Above average</span>
              </div>
            </div>
            <div className="p-3 bg-orange-100 rounded-lg">
              <Award size={24} className="text-orange-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Recent Projects */}
        <div className="lg:col-span-2 bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Recent Projects</h3>
            <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
              View All
            </button>
          </div>
          
          <div className="space-y-4">
            {recent_projects?.map((project, index) => (
              <div key={project.id || index} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Briefcase size={20} className="text-blue-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-gray-900">{project.name || 'Untitled Project'}</h4>
                    <p className="text-sm text-gray-500">{project.description || 'No description'}</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-4">
                  <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(project.status)}`}>
                    {project.status?.replace('-', ' ') || 'Unknown'}
                  </span>
                  <div className="flex items-center space-x-2">
                    <button className="text-gray-400 hover:text-gray-600">
                      <Eye size={16} />
                    </button>
                    <button className="text-gray-400 hover:text-gray-600">
                      <Edit size={16} />
                    </button>
                    <button className="text-gray-400 hover:text-gray-600">
                      <MoreVertical size={16} />
                    </button>
                  </div>
                </div>
              </div>
            ))}
            
            {(!recent_projects || recent_projects.length === 0) && (
              <div className="text-center py-8">
                <Briefcase size={48} className="mx-auto text-gray-300 mb-4" />
                <p className="text-gray-500">No projects found</p>
                <PermissionGate permission="projectKanbanBoards">
                  <button
                    onClick={() => setShowCreateForm(true)}
                    className="mt-2 text-blue-600 hover:text-blue-800 text-sm font-medium"
                  >
                    Create your first project
                  </button>
                </PermissionGate>
              </div>
            )}
          </div>
        </div>

        {/* Upcoming Deadlines */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Upcoming Deadlines</h3>
            <Clock size={20} className="text-gray-400" />
          </div>
          
          <div className="space-y-4">
            {upcoming_deadlines?.map((deadline) => (
              <div key={deadline.id} className="p-3 border border-gray-200 rounded-lg">
                <div className="flex items-center justify-between mb-2">
                  <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(deadline.priority)}`}>
                    {deadline.priority}
                  </span>
                  <span className="text-xs text-gray-500">{deadline.due_date}</span>
                </div>
                <h4 className="font-medium text-gray-900 text-sm">{deadline.task}</h4>
                <p className="text-xs text-gray-500 mt-1">{deadline.project}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectDashboard;
