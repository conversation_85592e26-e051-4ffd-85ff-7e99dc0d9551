/**
 * Project Analytics Component
 * Displays charts, progress reports, and team performance metrics
 * Styled consistently with Leave Management analytics
 */

import React, { useState, useEffect } from 'react';
import {
  Bar<PERSON>hart3,
  Pie<PERSON><PERSON>,
  TrendingUp,
  TrendingDown,
  Calendar,
  Clock,
  Users,
  Target,
  Award,
  AlertCircle,
  CheckCircle,
  Activity,
  Download,
  Filter,
  RefreshCw
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { filterAnalyticsByRole } from '../../utils/roleBasedFiltering';
import apiService from '../../services/api';

const ProjectAnalytics = ({ projectId = null, timeRange = '30' }) => {
  const [analyticsData, setAnalyticsData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [selectedTimeRange, setSelectedTimeRange] = useState(timeRange);
  const [selectedMetric, setSelectedMetric] = useState('overview');
  const { user, userRole } = useAuth();

  useEffect(() => {
    loadAnalytics();
  }, [projectId, selectedTimeRange]);

  const loadAnalytics = async () => {
    try {
      setLoading(true);
      // Mock analytics data for now
      const mockData = {
        overview: {
          total_projects: 12,
          active_projects: 8,
          completed_projects: 4,
          total_tasks: 156,
          completed_tasks: 98,
          overdue_tasks: 12,
          team_members: 15,
          avg_completion_time: 3.2
        },
        productivity: {
          tasks_completed_this_week: 23,
          tasks_completed_last_week: 18,
          productivity_trend: 'up',
          productivity_percentage: 27.8,
          velocity: 4.2,
          burndown_rate: 85
        },
        time_tracking: {
          total_hours_logged: 1240,
          billable_hours: 980,
          non_billable_hours: 260,
          avg_hours_per_task: 8.5,
          time_efficiency: 78
        },
        team_performance: [
          { name: 'John Doe', tasks_completed: 15, hours_logged: 120, efficiency: 92 },
          { name: 'Jane Smith', tasks_completed: 12, hours_logged: 96, efficiency: 88 },
          { name: 'Mike Johnson', tasks_completed: 18, hours_logged: 144, efficiency: 95 },
          { name: 'Sarah Wilson', tasks_completed: 10, hours_logged: 80, efficiency: 85 }
        ],
        project_status: [
          { status: 'Completed', count: 4, percentage: 33 },
          { status: 'In Progress', count: 6, percentage: 50 },
          { status: 'Planning', count: 2, percentage: 17 }
        ],
        task_distribution: [
          { priority: 'High', count: 25, percentage: 16 },
          { priority: 'Medium', count: 89, percentage: 57 },
          { priority: 'Low', count: 42, percentage: 27 }
        ]
      };
      
      // Apply role-based filtering to analytics data
      const filteredData = filterAnalyticsByRole(mockData, userRole, user?.id, user?.department);
      setAnalyticsData(filteredData);
    } catch (error) {
      console.error('Error loading analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status.toLowerCase()) {
      case 'completed': return 'text-green-600 bg-green-100';
      case 'in progress': return 'text-blue-600 bg-blue-100';
      case 'planning': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority.toLowerCase()) {
      case 'high': return 'text-red-600 bg-red-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-agno-primary"></div>
      </div>
    );
  }

  const { overview, productivity, time_tracking, team_performance, project_status, task_distribution } = analyticsData || {};

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Project Analytics</h2>
          <p className="text-gray-600 mt-1">Track performance and insights across your projects</p>
        </div>
        
        <div className="flex items-center space-x-3">
          <select
            value={selectedTimeRange}
            onChange={(e) => setSelectedTimeRange(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="7">Last 7 days</option>
            <option value="30">Last 30 days</option>
            <option value="90">Last 3 months</option>
            <option value="365">Last year</option>
          </select>
          
          <button
            onClick={loadAnalytics}
            className="flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
          >
            <RefreshCw size={16} className="mr-2" />
            Refresh
          </button>
          
          <button className="flex items-center px-4 py-2 agno-bg-primary text-white rounded-lg hover:bg-blue-700 transition-colors">
            <Download size={16} className="mr-2" />
            Export
          </button>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Project Completion Rate */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Completion Rate</p>
              <p className="text-2xl font-bold text-green-600 mt-1">
                {Math.round((overview?.completed_tasks / overview?.total_tasks) * 100) || 0}%
              </p>
              <div className="flex items-center mt-2">
                <TrendingUp size={14} className="text-green-500 mr-1" />
                <span className="text-sm text-green-600">+5.2% from last month</span>
              </div>
            </div>
            <div className="p-3 bg-green-100 rounded-lg">
              <CheckCircle size={24} className="text-green-600" />
            </div>
          </div>
        </div>

        {/* Team Productivity */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Team Productivity</p>
              <p className="text-2xl font-bold text-blue-600 mt-1">
                {productivity?.productivity_percentage || 0}%
              </p>
              <div className="flex items-center mt-2">
                {productivity?.productivity_trend === 'up' ? (
                  <TrendingUp size={14} className="text-green-500 mr-1" />
                ) : (
                  <TrendingDown size={14} className="text-red-500 mr-1" />
                )}
                <span className={`text-sm ${productivity?.productivity_trend === 'up' ? 'text-green-600' : 'text-red-600'}`}>
                  {productivity?.productivity_trend === 'up' ? '+' : '-'}27.8% this week
                </span>
              </div>
            </div>
            <div className="p-3 bg-blue-100 rounded-lg">
              <Activity size={24} className="text-blue-600" />
            </div>
          </div>
        </div>

        {/* Average Completion Time */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg. Completion Time</p>
              <p className="text-2xl font-bold text-purple-600 mt-1">
                {overview?.avg_completion_time || 0} days
              </p>
              <div className="flex items-center mt-2">
                <Clock size={14} className="text-purple-500 mr-1" />
                <span className="text-sm text-purple-600">2.1 days faster</span>
              </div>
            </div>
            <div className="p-3 bg-purple-100 rounded-lg">
              <Clock size={24} className="text-purple-600" />
            </div>
          </div>
        </div>

        {/* Team Efficiency */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Team Efficiency</p>
              <p className="text-2xl font-bold text-orange-600 mt-1">
                {time_tracking?.time_efficiency || 0}%
              </p>
              <div className="flex items-center mt-2">
                <Award size={14} className="text-orange-500 mr-1" />
                <span className="text-sm text-orange-600">Above target</span>
              </div>
            </div>
            <div className="p-3 bg-orange-100 rounded-lg">
              <Award size={24} className="text-orange-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Charts and Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Project Status Distribution */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Project Status Distribution</h3>
            <PieChart size={20} className="text-gray-400" />
          </div>
          
          <div className="space-y-4">
            {project_status?.map((item, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`w-3 h-3 rounded-full ${
                    item.status === 'Completed' ? 'bg-green-500' :
                    item.status === 'In Progress' ? 'bg-blue-500' : 'bg-yellow-500'
                  }`}></div>
                  <span className="text-sm font-medium text-gray-700">{item.status}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-600">{item.count}</span>
                  <span className="text-xs text-gray-500">({item.percentage}%)</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Task Priority Distribution */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Task Priority Distribution</h3>
            <BarChart3 size={20} className="text-gray-400" />
          </div>
          
          <div className="space-y-4">
            {task_distribution?.map((item, index) => (
              <div key={index} className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(item.priority)}`}>
                    {item.priority}
                  </span>
                  <span className="text-sm text-gray-600">{item.count} tasks</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className={`h-2 rounded-full ${
                      item.priority === 'High' ? 'bg-red-500' :
                      item.priority === 'Medium' ? 'bg-yellow-500' : 'bg-green-500'
                    }`}
                    style={{ width: `${item.percentage}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Team Performance Table */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">Team Performance</h3>
          <Users size={20} className="text-gray-400" />
        </div>
        
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-gray-200">
                <th className="text-left py-3 px-4 font-medium text-gray-700">Team Member</th>
                <th className="text-left py-3 px-4 font-medium text-gray-700">Tasks Completed</th>
                <th className="text-left py-3 px-4 font-medium text-gray-700">Hours Logged</th>
                <th className="text-left py-3 px-4 font-medium text-gray-700">Efficiency</th>
              </tr>
            </thead>
            <tbody>
              {team_performance?.map((member, index) => (
                <tr key={index} className="border-b border-gray-100 hover:bg-gray-50">
                  <td className="py-3 px-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm">
                        {member.name.split(' ').map(n => n[0]).join('')}
                      </div>
                      <span className="font-medium text-gray-900">{member.name}</span>
                    </div>
                  </td>
                  <td className="py-3 px-4 text-gray-600">{member.tasks_completed}</td>
                  <td className="py-3 px-4 text-gray-600">{member.hours_logged}h</td>
                  <td className="py-3 px-4">
                    <div className="flex items-center space-x-2">
                      <div className="w-16 bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-green-500 h-2 rounded-full"
                          style={{ width: `${member.efficiency}%` }}
                        ></div>
                      </div>
                      <span className="text-sm text-gray-600">{member.efficiency}%</span>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Time Tracking Summary */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">Time Tracking Summary</h3>
          <Clock size={20} className="text-gray-400" />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="text-center">
            <p className="text-2xl font-bold text-blue-600">{time_tracking?.total_hours_logged || 0}h</p>
            <p className="text-sm text-gray-600">Total Hours</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-green-600">{time_tracking?.billable_hours || 0}h</p>
            <p className="text-sm text-gray-600">Billable Hours</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-orange-600">{time_tracking?.non_billable_hours || 0}h</p>
            <p className="text-sm text-gray-600">Non-billable Hours</p>
          </div>
          <div className="text-center">
            <p className="text-2xl font-bold text-purple-600">{time_tracking?.avg_hours_per_task || 0}h</p>
            <p className="text-sm text-gray-600">Avg. per Task</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectAnalytics;
