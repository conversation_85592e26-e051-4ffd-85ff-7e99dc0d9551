from fastapi import APIRouter, HTTPException, status, Depends, Query
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID
from datetime import date

from ..core.security import get_current_user, require_permission, CurrentUser
from ..core.rbac import Permissions
from ..db.session import get_db
from ..schemas.attendance import (
    AttendanceRecordResponse, AttendanceListResponse, CheckInRequest,
    CheckOutRequest, BreakRequest, AttendanceStatusResponse,
    AttendanceApprovalRequest, BulkAttendanceUpdate
)
from ..services.shift_management.attendance_service import AttendanceService

router = APIRouter()
attendance_service = AttendanceService()


# Simple test endpoint
@router.get("/test")
async def test_attendance_api():
    """Test endpoint to verify attendance API is working"""
    return {"status": "ok", "message": "Attendance API is working"}


# Check-in/out endpoints
@router.post("/check-in/{employee_id}", response_model=AttendanceRecordResponse)
async def check_in(
    employee_id: UUID,
    check_in_data: CheckInRequest,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ATTENDANCE_CREATE))
):
    """Employee check-in"""
    return await attendance_service.check_in(db, employee_id, check_in_data, current_user)


@router.post("/check-out/{employee_id}", response_model=AttendanceRecordResponse)
async def check_out(
    employee_id: UUID,
    check_out_data: CheckOutRequest,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ATTENDANCE_CREATE))
):
    """Employee check-out"""
    return await attendance_service.check_out(db, employee_id, check_out_data, current_user)


@router.post("/break/{employee_id}", response_model=AttendanceRecordResponse)
async def manage_break(
    employee_id: UUID,
    break_data: BreakRequest,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ATTENDANCE_CREATE))
):
    """Manage employee break (start/end)"""
    return await attendance_service.manage_break(db, employee_id, break_data, current_user)


# Status and records endpoints
@router.get("/status/{employee_id}", response_model=AttendanceStatusResponse)
async def get_attendance_status(
    employee_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Get current attendance status for employee"""
    # Allow employees to check their own status
    if current_user.user_id != str(employee_id):
        # For now, allow all authenticated users to check attendance status
        # TODO: Implement proper permission checking
        pass

    return await attendance_service.get_attendance_status(db, employee_id, current_user)


@router.get("/")
async def get_attendance_records(
    employee_id: Optional[UUID] = Query(None),
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ATTENDANCE_READ))
):
    """Get attendance records with filtering"""
    try:
        return await attendance_service.get_attendance_records(
            db=db,
            employee_id=employee_id,
            start_date=start_date,
            end_date=end_date,
            skip=skip,
            limit=limit,
            current_user=current_user
        )
    except Exception as e:
        # Fallback to simple response if service fails
        return {
            "attendance_records": [],
            "total": 0,
            "skip": skip,
            "limit": limit,
            "message": "Attendance service temporarily unavailable"
        }


@router.get("/{record_id}", response_model=AttendanceRecordResponse)
async def get_attendance_record(
    record_id: UUID,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ATTENDANCE_READ))
):
    """Get specific attendance record"""
    records = await attendance_service.get_attendance_records(
        db=db,
        skip=0,
        limit=1,
        current_user=current_user
    )

    # Find the specific record
    record = next((r for r in records.records if r.id == record_id), None)
    if not record:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Attendance record not found"
        )

    return record


# Approval endpoints
@router.post("/approve", response_model=List[AttendanceRecordResponse])
async def approve_attendance(
    approval_data: AttendanceApprovalRequest,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ATTENDANCE_APPROVE))
):
    """Approve or reject attendance records"""
    return await attendance_service.approve_attendance(db, approval_data, current_user)


# Bulk operations
@router.post("/bulk-update")
async def bulk_update_attendance(
    bulk_data: BulkAttendanceUpdate,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(require_permission(Permissions.ATTENDANCE_UPDATE))
):
    """Bulk update attendance records"""
    # This would be implemented for bulk operations
    return {"message": f"Bulk update for {len(bulk_data.employee_ids)} employees on {bulk_data.date}"}


# My attendance endpoints (for employees to view their own)
@router.get("/my/records", response_model=AttendanceListResponse)
async def get_my_attendance(
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Get my attendance records"""
    return await attendance_service.get_attendance_records(
        db=db,
        employee_id=UUID(current_user.user_id),
        start_date=start_date,
        end_date=end_date,
        skip=skip,
        limit=limit,
        current_user=current_user
    )


@router.get("/my/status", response_model=AttendanceStatusResponse)
async def get_my_attendance_status(
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Get my current attendance status"""
    return await attendance_service.get_attendance_status(
        db, UUID(current_user.user_id), current_user
    )


# Simplified status endpoint for testing
@router.get("/my/status-simple")
async def get_my_attendance_status_simple(
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """Get my current attendance status - simplified version"""
    try:
        result = await attendance_service.get_attendance_status(
            db, UUID(current_user.user_id), current_user
        )
        return {
            "status": "success",
            "data": result,
            "user_id": current_user.user_id
        }
    except Exception as e:
        return {
            "status": "error",
            "error": str(e),
            "user_id": current_user.user_id
        }


@router.post("/my/check-in", response_model=AttendanceRecordResponse)
async def my_check_in(
    check_in_data: CheckInRequest,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """My check-in"""
    return await attendance_service.check_in(
        db, UUID(current_user.user_id), check_in_data, current_user
    )


@router.post("/my/check-out", response_model=AttendanceRecordResponse)
async def my_check_out(
    check_out_data: CheckOutRequest,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """My check-out"""
    return await attendance_service.check_out(
        db, UUID(current_user.user_id), check_out_data, current_user
    )


@router.post("/my/break", response_model=AttendanceRecordResponse)
async def my_break(
    break_data: BreakRequest,
    db: Session = Depends(get_db),
    current_user: CurrentUser = Depends(get_current_user)
):
    """My break management"""
    return await attendance_service.manage_break(
        db, UUID(current_user.user_id), break_data, current_user
    )
