/**
 * Comprehensive Project Management Test Suite
 * Tests all project management functionality including RBAC, UI components, and workflows
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { AuthProvider } from '../contexts/AuthContext';
import Projects from '../pages/Projects';
import ProjectDashboard from '../components/project/ProjectDashboard';
import ProjectList from '../components/project/ProjectList';
import TaskBoard from '../components/project/TaskBoard';
import { usePermissions } from '../hooks/usePermissions';

// Mock the hooks and services
jest.mock('../hooks/usePermissions');
jest.mock('../services/api');
jest.mock('../hooks/useProjectData');

const mockProjects = [
  {
    id: '1',
    name: 'HRMS Development',
    description: 'Human Resource Management System',
    status: 'in-progress',
    priority: 'high',
    progress: 65,
    start_date: '2024-01-01',
    end_date: '2024-06-30',
    tasksCompleted: 15,
    tasksTotal: 25,
    teamMembers: ['<PERSON>', '<PERSON>']
  },
  {
    id: '2',
    name: 'Mobile App',
    description: 'Mobile application for HR system',
    status: 'planning',
    priority: 'medium',
    progress: 20,
    start_date: '2024-02-01',
    end_date: '2024-08-30',
    tasksCompleted: 3,
    tasksTotal: 18,
    teamMembers: ['Mike Johnson']
  }
];

const mockTasks = [
  {
    id: '1',
    title: 'User Authentication',
    description: 'Implement user login and registration',
    status: 'in-progress',
    priority: 'high',
    assignee: { first_name: 'John', last_name: 'Doe' },
    due_date: '2024-01-15',
    project_id: '1'
  },
  {
    id: '2',
    title: 'Database Schema',
    description: 'Design database structure',
    status: 'completed',
    priority: 'medium',
    assignee: { first_name: 'Jane', last_name: 'Smith' },
    due_date: '2024-01-10',
    project_id: '1'
  }
];

const TestWrapper = ({ children, userRole = 'EMPLOYEE' }) => {
  const mockUser = {
    id: '1',
    name: 'Test User',
    email: '<EMAIL>',
    role: userRole
  };

  return (
    <BrowserRouter>
      <AuthProvider value={{ user: mockUser, userRole }}>
        {children}
      </AuthProvider>
    </BrowserRouter>
  );
};

describe('Project Management System', () => {
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Mock useProjectData hooks
    require('../hooks/useProjectData').useProjects.mockReturnValue({
      projects: mockProjects,
      loading: false,
      error: null,
      createProject: jest.fn(),
      updateProject: jest.fn(),
      deleteProject: jest.fn(),
      refreshProjects: jest.fn()
    });

    require('../hooks/useProjectData').useTasks.mockReturnValue({
      tasks: mockTasks,
      loading: false,
      error: null,
      createTask: jest.fn(),
      updateTask: jest.fn(),
      updateTaskStatus: jest.fn(),
      deleteTask: jest.fn(),
      refreshTasks: jest.fn()
    });

    require('../hooks/useProjectData').useProjectDashboard.mockReturnValue({
      dashboardData: {
        summary: {
          total_projects: 2,
          active_projects: 1,
          completed_projects: 1,
          team_members: 3
        }
      },
      loading: false,
      error: null,
      refreshDashboard: jest.fn()
    });
  });

  describe('Role-Based Access Control', () => {
    test('SuperAdmin should see all project management features', () => {
      usePermissions.mockReturnValue({
        canAccessResource: jest.fn().mockReturnValue(true),
        getPermissionType: jest.fn().mockReturnValue('FULL')
      });

      render(
        <TestWrapper userRole="SUPER_ADMIN">
          <Projects />
        </TestWrapper>
      );

      expect(screen.getByText('Project Management')).toBeInTheDocument();
      expect(screen.getByText('Dashboard')).toBeInTheDocument();
      expect(screen.getByText('Projects')).toBeInTheDocument();
      expect(screen.getByText('Kanban Board')).toBeInTheDocument();
      expect(screen.getByText('Analytics')).toBeInTheDocument();
    });

    test('Manager should see team-only project access', () => {
      usePermissions.mockReturnValue({
        canAccessResource: jest.fn().mockReturnValue(true),
        getPermissionType: jest.fn().mockReturnValue('TEAM_ONLY')
      });

      render(
        <TestWrapper userRole="MANAGER">
          <Projects />
        </TestWrapper>
      );

      expect(screen.getByText('Project Management')).toBeInTheDocument();
      // Should see project management interface
      expect(screen.getByText('Dashboard')).toBeInTheDocument();
    });

    test('Employee should see assigned-only project access', () => {
      usePermissions.mockReturnValue({
        canAccessResource: jest.fn().mockReturnValue(true),
        getPermissionType: jest.fn().mockReturnValue('ASSIGNED_ONLY')
      });

      render(
        <TestWrapper userRole="EMPLOYEE">
          <Projects />
        </TestWrapper>
      );

      expect(screen.getByText('Project Management')).toBeInTheDocument();
      // Should see limited project management interface
      expect(screen.getByText('Dashboard')).toBeInTheDocument();
    });
  });

  describe('Project Dashboard', () => {
    test('should display project statistics correctly', () => {
      usePermissions.mockReturnValue({
        canAccessResource: jest.fn().mockReturnValue(true)
      });

      render(
        <TestWrapper>
          <ProjectDashboard />
        </TestWrapper>
      );

      expect(screen.getByText('Project Management')).toBeInTheDocument();
      expect(screen.getByText('Total Projects')).toBeInTheDocument();
      expect(screen.getByText('Active Projects')).toBeInTheDocument();
      expect(screen.getByText('Team Members')).toBeInTheDocument();
    });

    test('should handle loading state', () => {
      require('../hooks/useProjectData').useProjectDashboard.mockReturnValue({
        dashboardData: null,
        loading: true,
        error: null
      });

      render(
        <TestWrapper>
          <ProjectDashboard />
        </TestWrapper>
      );

      expect(screen.getByRole('status')).toBeInTheDocument(); // Loading spinner
    });

    test('should handle error state', () => {
      require('../hooks/useProjectData').useProjectDashboard.mockReturnValue({
        dashboardData: null,
        loading: false,
        error: 'Failed to load dashboard',
        refreshDashboard: jest.fn()
      });

      render(
        <TestWrapper>
          <ProjectDashboard />
        </TestWrapper>
      );

      expect(screen.getByText(/Error loading dashboard/)).toBeInTheDocument();
      expect(screen.getByText('Retry')).toBeInTheDocument();
    });
  });

  describe('Project List', () => {
    test('should display projects correctly', () => {
      const mockHandlers = {
        onCreateProject: jest.fn(),
        onEditProject: jest.fn(),
        onViewProject: jest.fn()
      };

      render(
        <TestWrapper>
          <ProjectList {...mockHandlers} />
        </TestWrapper>
      );

      expect(screen.getByText('HRMS Development')).toBeInTheDocument();
      expect(screen.getByText('Mobile App')).toBeInTheDocument();
      expect(screen.getByText('Human Resource Management System')).toBeInTheDocument();
    });

    test('should filter projects by status', async () => {
      const mockHandlers = {
        onCreateProject: jest.fn(),
        onEditProject: jest.fn(),
        onViewProject: jest.fn()
      };

      render(
        <TestWrapper>
          <ProjectList {...mockHandlers} />
        </TestWrapper>
      );

      const statusFilter = screen.getByDisplayValue('All Status');
      fireEvent.change(statusFilter, { target: { value: 'in-progress' } });

      // Should trigger filtering
      expect(statusFilter.value).toBe('in-progress');
    });

    test('should search projects', async () => {
      const mockHandlers = {
        onCreateProject: jest.fn(),
        onEditProject: jest.fn(),
        onViewProject: jest.fn()
      };

      render(
        <TestWrapper>
          <ProjectList {...mockHandlers} />
        </TestWrapper>
      );

      const searchInput = screen.getByPlaceholderText('Search projects...');
      fireEvent.change(searchInput, { target: { value: 'HRMS' } });

      expect(searchInput.value).toBe('HRMS');
    });
  });

  describe('Task Board (Kanban)', () => {
    test('should display kanban columns', () => {
      const mockHandlers = {
        onCreateTask: jest.fn(),
        onEditTask: jest.fn(),
        onViewTask: jest.fn()
      };

      render(
        <TestWrapper>
          <TaskBoard projectId="1" {...mockHandlers} />
        </TestWrapper>
      );

      expect(screen.getByText('To Do')).toBeInTheDocument();
      expect(screen.getByText('In Progress')).toBeInTheDocument();
      expect(screen.getByText('Review')).toBeInTheDocument();
      expect(screen.getByText('Done')).toBeInTheDocument();
    });

    test('should display tasks in correct columns', () => {
      const mockHandlers = {
        onCreateTask: jest.fn(),
        onEditTask: jest.fn(),
        onViewTask: jest.fn()
      };

      render(
        <TestWrapper>
          <TaskBoard projectId="1" {...mockHandlers} />
        </TestWrapper>
      );

      expect(screen.getByText('User Authentication')).toBeInTheDocument();
      expect(screen.getByText('Database Schema')).toBeInTheDocument();
    });

    test('should handle task creation', () => {
      const mockHandlers = {
        onCreateTask: jest.fn(),
        onEditTask: jest.fn(),
        onViewTask: jest.fn()
      };

      usePermissions.mockReturnValue({
        canAccessResource: jest.fn().mockReturnValue(true)
      });

      render(
        <TestWrapper>
          <TaskBoard projectId="1" {...mockHandlers} />
        </TestWrapper>
      );

      const addTaskButton = screen.getByText('Add Task');
      fireEvent.click(addTaskButton);

      expect(mockHandlers.onCreateTask).toHaveBeenCalled();
    });
  });

  describe('Navigation and Tabs', () => {
    test('should switch between tabs correctly', () => {
      usePermissions.mockReturnValue({
        canAccessResource: jest.fn().mockReturnValue(true)
      });

      render(
        <TestWrapper>
          <Projects />
        </TestWrapper>
      );

      // Test tab navigation
      const projectsTab = screen.getByText('Projects');
      fireEvent.click(projectsTab);

      const kanbanTab = screen.getByText('Kanban Board');
      fireEvent.click(kanbanTab);

      const analyticsTab = screen.getByText('Analytics');
      fireEvent.click(analyticsTab);

      // Tabs should be clickable and change active state
      expect(projectsTab).toBeInTheDocument();
      expect(kanbanTab).toBeInTheDocument();
      expect(analyticsTab).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    test('should handle API errors gracefully', () => {
      require('../hooks/useProjectData').useProjects.mockReturnValue({
        projects: [],
        loading: false,
        error: 'Network error',
        createProject: jest.fn(),
        updateProject: jest.fn(),
        deleteProject: jest.fn(),
        refreshProjects: jest.fn()
      });

      render(
        <TestWrapper>
          <ProjectList 
            onCreateProject={jest.fn()}
            onEditProject={jest.fn()}
            onViewProject={jest.fn()}
          />
        </TestWrapper>
      );

      // Should show empty state or error message
      expect(screen.getByText('No projects found')).toBeInTheDocument();
    });
  });
});
