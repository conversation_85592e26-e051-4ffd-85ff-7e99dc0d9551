/**
 * Real-time Activity Feed Component
 * Displays live project and task activities with WebSocket integration
 * Styled consistently with Leave Management components
 */

import React, { useState, useEffect } from 'react';
import {
  Activity,
  User,
  Clock,
  MessageSquare,
  FileText,
  CheckCircle,
  XCircle,
  Edit,
  Plus,
  Target,
  Calendar,
  RefreshCw,
  Filter,
  Bell
} from 'lucide-react';

const ActivityFeed = ({ projectId = null, limit = 20 }) => {
  const [activities, setActivities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('all');
  const [isLive, setIsLive] = useState(true);

  useEffect(() => {
    loadActivities();
    
    // Setup WebSocket connection for real-time updates
    if (isLive) {
      setupWebSocket();
    }
    
    return () => {
      // Cleanup WebSocket connection
      if (window.projectWebSocket) {
        window.projectWebSocket.close();
      }
    };
  }, [projectId, filter]);

  const loadActivities = async () => {
    try {
      setLoading(true);
      // Mock activity data for now
      const mockActivities = [
        {
          id: 1,
          type: 'task_created',
          user: { name: '<PERSON>e', avatar: 'JD' },
          action: 'created task',
          target: 'User Authentication Module',
          project: 'HRMS Development',
          timestamp: new Date(Date.now() - 5 * 60 * 1000),
          details: { priority: 'high', status: 'todo' }
        },
        {
          id: 2,
          type: 'task_completed',
          user: { name: 'Jane Smith', avatar: 'JS' },
          action: 'completed task',
          target: 'Database Schema Design',
          project: 'HRMS Development',
          timestamp: new Date(Date.now() - 15 * 60 * 1000),
          details: { duration: '3.5 hours' }
        },
        {
          id: 3,
          type: 'comment_added',
          user: { name: 'Mike Johnson', avatar: 'MJ' },
          action: 'commented on',
          target: 'API Integration',
          project: 'Mobile App',
          timestamp: new Date(Date.now() - 30 * 60 * 1000),
          details: { comment: 'Need to review the authentication flow' }
        },
        {
          id: 4,
          type: 'project_updated',
          user: { name: 'Sarah Wilson', avatar: 'SW' },
          action: 'updated project',
          target: 'Mobile App',
          project: 'Mobile App',
          timestamp: new Date(Date.now() - 45 * 60 * 1000),
          details: { field: 'status', from: 'planning', to: 'in-progress' }
        },
        {
          id: 5,
          type: 'task_assigned',
          user: { name: 'Alex Brown', avatar: 'AB' },
          action: 'assigned task',
          target: 'UI Component Library',
          project: 'HRMS Development',
          timestamp: new Date(Date.now() - 60 * 60 * 1000),
          details: { assignee: 'John Doe' }
        },
        {
          id: 6,
          type: 'milestone_reached',
          user: { name: 'System', avatar: 'SY' },
          action: 'milestone reached',
          target: 'Phase 1 Complete',
          project: 'HRMS Development',
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
          details: { completion: '100%' }
        }
      ];
      
      setActivities(mockActivities);
    } catch (error) {
      console.error('Error loading activities:', error);
    } finally {
      setLoading(false);
    }
  };

  const setupWebSocket = () => {
    try {
      const wsUrl = `ws://localhost:8000/ws/project-activities${projectId ? `?project_id=${projectId}` : ''}`;
      const ws = new WebSocket(wsUrl);
      
      ws.onopen = () => {
        console.log('Activity feed WebSocket connected');
      };
      
      ws.onmessage = (event) => {
        const activity = JSON.parse(event.data);
        setActivities(prev => [activity, ...prev].slice(0, limit));
      };
      
      ws.onclose = () => {
        console.log('Activity feed WebSocket disconnected');
        // Attempt to reconnect after 5 seconds
        setTimeout(() => {
          if (isLive) {
            setupWebSocket();
          }
        }, 5000);
      };
      
      ws.onerror = (error) => {
        console.error('Activity feed WebSocket error:', error);
      };
      
      window.projectWebSocket = ws;
    } catch (error) {
      console.error('Error setting up WebSocket:', error);
    }
  };

  const getActivityIcon = (type) => {
    switch (type) {
      case 'task_created': return <Plus size={16} className="text-blue-600" />;
      case 'task_completed': return <CheckCircle size={16} className="text-green-600" />;
      case 'task_updated': return <Edit size={16} className="text-yellow-600" />;
      case 'task_assigned': return <User size={16} className="text-purple-600" />;
      case 'comment_added': return <MessageSquare size={16} className="text-blue-600" />;
      case 'project_updated': return <FileText size={16} className="text-orange-600" />;
      case 'milestone_reached': return <Target size={16} className="text-green-600" />;
      default: return <Activity size={16} className="text-gray-600" />;
    }
  };

  const getActivityColor = (type) => {
    switch (type) {
      case 'task_created': return 'bg-blue-100 border-blue-200';
      case 'task_completed': return 'bg-green-100 border-green-200';
      case 'task_updated': return 'bg-yellow-100 border-yellow-200';
      case 'task_assigned': return 'bg-purple-100 border-purple-200';
      case 'comment_added': return 'bg-blue-100 border-blue-200';
      case 'project_updated': return 'bg-orange-100 border-orange-200';
      case 'milestone_reached': return 'bg-green-100 border-green-200';
      default: return 'bg-gray-100 border-gray-200';
    }
  };

  const formatTimeAgo = (timestamp) => {
    const now = new Date();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(diff / 3600000);
    const days = Math.floor(diff / 86400000);
    
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return `${days}d ago`;
  };

  const filteredActivities = activities.filter(activity => {
    if (filter === 'all') return true;
    return activity.type.includes(filter);
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-32">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-agno-primary"></div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-2">
          <Activity size={20} className="text-gray-600" />
          <h3 className="text-lg font-semibold text-gray-900">Activity Feed</h3>
          {isLive && (
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-xs text-green-600">Live</span>
            </div>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            className="text-sm px-2 py-1 border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">All Activities</option>
            <option value="task">Tasks</option>
            <option value="comment">Comments</option>
            <option value="project">Projects</option>
          </select>
          
          <button
            onClick={() => setIsLive(!isLive)}
            className={`p-1 rounded transition-colors ${
              isLive ? 'text-green-600 hover:bg-green-50' : 'text-gray-400 hover:bg-gray-50'
            }`}
            title={isLive ? 'Disable live updates' : 'Enable live updates'}
          >
            <Bell size={16} />
          </button>
          
          <button
            onClick={loadActivities}
            className="p-1 text-gray-400 hover:text-gray-600 hover:bg-gray-50 rounded transition-colors"
            title="Refresh activities"
          >
            <RefreshCw size={16} />
          </button>
        </div>
      </div>

      {/* Activity List */}
      <div className="space-y-4 max-h-96 overflow-y-auto">
        {filteredActivities.length > 0 ? (
          filteredActivities.map((activity) => (
            <div
              key={activity.id}
              className={`flex items-start space-x-3 p-3 rounded-lg border ${getActivityColor(activity.type)}`}
            >
              {/* User Avatar */}
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs font-medium">
                  {activity.user.avatar}
                </div>
              </div>
              
              {/* Activity Content */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2 mb-1">
                  {getActivityIcon(activity.type)}
                  <p className="text-sm text-gray-900">
                    <span className="font-medium">{activity.user.name}</span>
                    <span className="mx-1">{activity.action}</span>
                    <span className="font-medium">{activity.target}</span>
                    {activity.project && (
                      <>
                        <span className="mx-1">in</span>
                        <span className="text-blue-600">{activity.project}</span>
                      </>
                    )}
                  </p>
                </div>
                
                {/* Activity Details */}
                {activity.details && (
                  <div className="text-xs text-gray-600 mb-2">
                    {activity.type === 'task_created' && (
                      <span>Priority: {activity.details.priority}, Status: {activity.details.status}</span>
                    )}
                    {activity.type === 'task_completed' && (
                      <span>Duration: {activity.details.duration}</span>
                    )}
                    {activity.type === 'comment_added' && (
                      <span>"{activity.details.comment}"</span>
                    )}
                    {activity.type === 'project_updated' && (
                      <span>{activity.details.field}: {activity.details.from} → {activity.details.to}</span>
                    )}
                    {activity.type === 'task_assigned' && (
                      <span>Assigned to: {activity.details.assignee}</span>
                    )}
                    {activity.type === 'milestone_reached' && (
                      <span>Completion: {activity.details.completion}</span>
                    )}
                  </div>
                )}
                
                {/* Timestamp */}
                <div className="flex items-center space-x-1 text-xs text-gray-500">
                  <Clock size={12} />
                  <span>{formatTimeAgo(activity.timestamp)}</span>
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="text-center py-8">
            <Activity size={32} className="mx-auto text-gray-300 mb-2" />
            <p className="text-gray-500 text-sm">No activities found</p>
            {filter !== 'all' && (
              <button
                onClick={() => setFilter('all')}
                className="mt-2 text-blue-600 hover:text-blue-800 text-xs"
              >
                Show all activities
              </button>
            )}
          </div>
        )}
      </div>
      
      {/* Load More */}
      {filteredActivities.length >= limit && (
        <div className="mt-4 text-center">
          <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
            Load more activities
          </button>
        </div>
      )}
    </div>
  );
};

export default ActivityFeed;
