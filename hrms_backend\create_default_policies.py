#!/usr/bin/env python3

import requests
import json

def create_default_leave_policies():
    """Create default leave policies if they don't exist"""
    
    base_url = "http://localhost:8085"
    
    # First, let's try to get a token
    print("🔐 Getting authentication token...")
    
    # Try to login as admin
    login_data = {
        "email": "<EMAIL>",
        "password": "admin123"
    }
    
    try:
        response = requests.post(f"{base_url}/api/auth/login", json=login_data)
        print(f"Login Status: {response.status_code}")
        
        if response.status_code == 200:
            token_data = response.json()
            token = token_data.get('access_token')
            print(f"✅ Got token: {token[:20]}...")
            
            headers = {"Authorization": f"Bearer {token}"}
            
            # Check existing policies first
            print("\n📋 Checking existing leave policies...")
            response = requests.get(f"{base_url}/api/leave/policies", headers=headers)
            
            if response.status_code == 200:
                existing_policies = response.json()
                print(f"Found {len(existing_policies)} existing policies")
                
                if len(existing_policies) == 0:
                    print("🏗️ Creating default leave policies...")
                    
                    # Default leave policies
                    default_policies = [
                        {
                            "name": "Annual Leave",
                            "leave_type": "ANNUAL",
                            "annual_entitlement": 21,
                            "max_carry_forward": 5,
                            "accrual_frequency": "monthly",
                            "accrual_start_date": "hire_date",
                            "min_notice_days": 7,
                            "max_consecutive_days": 15,
                            "min_application_days": 0.5,
                            "requires_approval": True,
                            "requires_documentation": False,
                            "available_during_probation": True
                        },
                        {
                            "name": "Sick Leave",
                            "leave_type": "SICK",
                            "annual_entitlement": 10,
                            "max_carry_forward": 2,
                            "accrual_frequency": "monthly",
                            "accrual_start_date": "hire_date",
                            "min_notice_days": 0,
                            "max_consecutive_days": 30,
                            "min_application_days": 0.5,
                            "requires_approval": True,
                            "requires_documentation": True,
                            "documentation_threshold": 3,
                            "available_during_probation": True
                        },
                        {
                            "name": "Personal Leave",
                            "leave_type": "PERSONAL",
                            "annual_entitlement": 5,
                            "max_carry_forward": 0,
                            "accrual_frequency": "yearly",
                            "accrual_start_date": "calendar_year",
                            "min_notice_days": 3,
                            "max_consecutive_days": 5,
                            "min_application_days": 0.5,
                            "requires_approval": True,
                            "requires_documentation": False,
                            "available_during_probation": False
                        },
                        {
                            "name": "Maternity Leave",
                            "leave_type": "MATERNITY",
                            "annual_entitlement": 90,
                            "max_carry_forward": 0,
                            "accrual_frequency": "yearly",
                            "accrual_start_date": "hire_date",
                            "min_notice_days": 30,
                            "max_consecutive_days": 90,
                            "min_application_days": 1,
                            "requires_approval": True,
                            "requires_documentation": True,
                            "documentation_threshold": 1,
                            "applicable_genders": ["FEMALE"],
                            "available_during_probation": True
                        }
                    ]
                    
                    # Create each policy
                    for policy_data in default_policies:
                        print(f"Creating {policy_data['name']}...")
                        response = requests.post(
                            f"{base_url}/api/leave/policies", 
                            json=policy_data, 
                            headers=headers
                        )
                        
                        if response.status_code == 201:
                            print(f"✅ Created {policy_data['name']}")
                        else:
                            print(f"❌ Failed to create {policy_data['name']}: {response.status_code}")
                            print(f"Error: {response.text}")
                    
                    print("\n🎉 Default leave policies created!")
                    
                else:
                    print("✅ Leave policies already exist:")
                    for policy in existing_policies:
                        print(f"  - {policy['name']} ({policy['leave_type']}) - {policy['annual_entitlement']} days/year")
                        
            else:
                print(f"❌ Error checking policies: {response.status_code}")
                print(f"Response: {response.text}")
                
        else:
            print(f"❌ Login failed: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    create_default_leave_policies()
