/**
 * Custom React Hooks for RBAC Permission Management
 * Provides easy-to-use hooks for checking permissions throughout the application
 * 
 * Features:
 * - Simple permission checking hooks
 * - Role-based access control utilities
 * - Team and assignment-based permission checking
 * - Memoized results for performance optimization
 */

import { useMemo } from 'react';
import { useAuth } from '../contexts/AuthContext';
import {
  hasPermission,
  getPermissionType,
  hasFullAccess,
  hasTeamOnlyAccess,
  hasAssignedOnlyAccess,
  hasSelfOnlyAccess,
  isRoleHigher,
  getSubordinateRoles,
  PERMISSION_TYPES,
  ROLES
} from '../services/permissions';

/**
 * Main permission hook - provides all permission checking functions
 * @returns {Object} Permission checking functions and utilities
 */
export function usePermissions() {
  const { userRole, userId, user } = useAuth();

  // Memoize permission functions to avoid unnecessary recalculations
  const permissions = useMemo(() => {
    if (!userRole) {
      return {
        hasPermission: () => false,
        getPermissionType: () => PERMISSION_TYPES.NONE,
        hasFullAccess: () => false,
        hasTeamOnlyAccess: () => false,
        hasAssignedOnlyAccess: () => false,
        hasSelfOnlyAccess: () => false,
        canAccessResource: () => false,
        isHigherRole: () => false,
        getSubordinateRoles: () => [],
        userRole: null,
        userId: null
      };
    }

    return {
      // Basic permission checking
      hasPermission: (permission) => hasPermission(userRole, permission),
      getPermissionType: (permission) => getPermissionType(userRole, permission),
      hasFullAccess: (permission) => hasFullAccess(userRole, permission),
      hasTeamOnlyAccess: (permission) => hasTeamOnlyAccess(userRole, permission),
      hasAssignedOnlyAccess: (permission) => hasAssignedOnlyAccess(userRole, permission),
      hasSelfOnlyAccess: (permission) => hasSelfOnlyAccess(userRole, permission),

      // Advanced permission checking with context
      canAccessResource: (permission, resourceContext = {}) => 
        canAccessResource(userRole, userId, permission, resourceContext),

      // Role hierarchy functions
      isHigherRole: (otherRole) => isRoleHigher(userRole, otherRole),
      getSubordinateRoles: () => getSubordinateRoles(userRole),

      // User context
      userRole,
      userId,
      user
    };
  }, [userRole, userId, user]);

  return permissions;
}

/**
 * Hook for checking specific permission with optional context
 * @param {string} permission - Permission to check
 * @param {Object} context - Additional context for permission checking
 * @returns {Object} Permission status and type
 */
export function usePermission(permission, context = {}) {
  const { userRole, userId } = useAuth();

  return useMemo(() => {
    if (!userRole || !permission) {
      return {
        hasAccess: false,
        permissionType: PERMISSION_TYPES.NONE,
        canView: false,
        canEdit: false,
        canDelete: false
      };
    }

    const permissionType = getPermissionType(userRole, permission);
    const hasAccess = permissionType !== PERMISSION_TYPES.NONE;
    const canAccessRes = canAccessResource(userRole, userId, permission, context);

    return {
      hasAccess,
      permissionType,
      canView: hasAccess && canAccessRes,
      canEdit: hasAccess && canAccessRes && permissionType !== PERMISSION_TYPES.SELF_ONLY,
      canDelete: hasAccess && canAccessRes && (permissionType === PERMISSION_TYPES.FULL)
    };
  }, [userRole, userId, permission, context]);
}

/**
 * Hook for checking multiple permissions at once
 * @param {string[]} permissions - Array of permissions to check
 * @returns {Object} Object with permission names as keys and boolean values
 */
export function useMultiplePermissions(permissions = []) {
  const { userRole } = useAuth();

  return useMemo(() => {
    if (!userRole || !Array.isArray(permissions)) {
      return {};
    }

    return permissions.reduce((acc, permission) => {
      acc[permission] = hasPermission(userRole, permission);
      return acc;
    }, {});
  }, [userRole, permissions]);
}

/**
 * Hook for role-based conditional rendering
 * @param {string|string[]} allowedRoles - Role or array of roles that have access
 * @returns {boolean} Whether current user role is allowed
 */
export function useRoleAccess(allowedRoles) {
  const { userRole } = useAuth();

  return useMemo(() => {
    if (!userRole) return false;
    
    if (Array.isArray(allowedRoles)) {
      return allowedRoles.includes(userRole);
    }
    
    return userRole === allowedRoles;
  }, [userRole, allowedRoles]);
}

/**
 * Hook for checking if user can manage other users (team members, subordinates)
 * @param {Object} targetUser - User object to check access against
 * @returns {boolean} Whether current user can manage the target user
 */
export function useCanManageUser(targetUser) {
  const { userRole, userId, user } = useAuth();

  return useMemo(() => {
    if (!userRole || !targetUser) return false;

    // Super admin and admin can manage everyone
    if (userRole === ROLES.SUPER_ADMIN || userRole === ROLES.ADMIN) {
      return true;
    }

    // HR can manage non-admin users
    if (userRole === ROLES.HR) {
      return ![ROLES.SUPER_ADMIN, ROLES.ADMIN].includes(targetUser.role);
    }

    // Managers can manage their team members
    if (userRole === ROLES.MANAGER) {
      return targetUser.managerId === userId || 
             targetUser.department === user?.department;
    }

    // Employees cannot manage other users
    return false;
  }, [userRole, userId, user, targetUser]);
}

/**
 * Hook for checking if user can access team-related resources
 * @param {Object} resource - Resource object with team/assignment information
 * @returns {boolean} Whether user can access the resource
 */
export function useTeamAccess(resource) {
  const { userRole, userId, user } = useAuth();

  return useMemo(() => {
    if (!userRole || !resource) return false;

    // Full access roles
    if ([ROLES.SUPER_ADMIN, ROLES.ADMIN, ROLES.HR].includes(userRole)) {
      return true;
    }

    // Manager access to team resources
    if (userRole === ROLES.MANAGER) {
      return resource.managerId === userId ||
             resource.department === user?.department ||
             resource.teamId === user?.teamId;
    }

    // Employee access to assigned resources
    if (userRole === ROLES.EMPLOYEE) {
      return resource.assignedTo === userId ||
             resource.createdBy === userId ||
             (resource.assignedUsers && resource.assignedUsers.includes(userId));
    }

    return false;
  }, [userRole, userId, user, resource]);
}

/**
 * Advanced resource access checking with context
 * @param {string} userRole - User role
 * @param {string} userId - User ID
 * @param {string} permission - Permission to check
 * @param {Object} context - Resource context
 * @returns {boolean} Whether user can access the resource
 */
function canAccessResource(userRole, userId, permission, context = {}) {
  const permissionType = getPermissionType(userRole, permission);

  switch (permissionType) {
    case PERMISSION_TYPES.FULL:
      return true;

    case PERMISSION_TYPES.TEAM_ONLY:
      // If no context provided (page-level access), allow access
      if (Object.keys(context).length === 0) {
        return true;
      }
      return context.isTeamMember ||
             context.managerId === userId ||
             context.department === context.userDepartment;

    case PERMISSION_TYPES.ASSIGNED_ONLY:
      // If no context provided (page-level access), allow access
      // Content filtering will be handled at the component level
      if (Object.keys(context).length === 0) {
        return true;
      }
      return context.assignedTo === userId ||
             context.createdBy === userId ||
             (context.assignedUsers && context.assignedUsers.includes(userId));

    case PERMISSION_TYPES.SELF_ONLY:
      // If no context provided (page-level access), allow access
      if (Object.keys(context).length === 0) {
        return true;
      }
      return context.resourceUserId === userId;

    case PERMISSION_TYPES.NONE:
    default:
      return false;
  }
}

/**
 * Hook for navigation permissions - checks if user can access specific routes
 * @param {string} route - Route path to check
 * @returns {boolean} Whether user can access the route
 */
export function useRouteAccess(route) {
  const permissions = usePermissions();

  return useMemo(() => {
    // Route to permission mapping
    const routePermissions = {
      '/dashboard': 'dashboard',
      '/employees': 'employeeDirectory',
      '/profile': 'profileSelf',
      '/attendance': 'attendanceTrackerSelf',
      '/attendance/manage': 'attendanceManagement',
      '/leave': 'leaveRequestsSelf',
      '/leave/manage': 'leaveManagement',
      '/payroll': 'payrollPayslipsSelf',
      '/payroll/manage': 'payrollManagement',
      '/projects': 'projectKanbanBoards',
      '/tasks': 'taskManagement',
      '/time-tracker': 'timeTracker',
      '/tickets': 'ticketingSystemSelf',
      '/tickets/manage': 'ticketManagement',
      '/performance': 'performanceReviewsSelf',
      '/performance/team': 'performanceReviewsTeam',
      '/settings': 'systemSettings',
      '/logs': 'systemLogs'
    };

    const permission = routePermissions[route];
    return permission ? permissions.hasPermission(permission) : false;
  }, [route, permissions]);
}
