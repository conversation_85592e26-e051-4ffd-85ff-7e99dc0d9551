/**
 * Project List Component
 * Displays projects in a grid/list view with filtering and search
 * Styled consistently with Leave Management components
 */

import React, { useState, useEffect } from 'react';
import {
  Search,
  Filter,
  Plus,
  Eye,
  Edit,
  MoreVertical,
  Calendar,
  Users,
  Target,
  Clock,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { usePermissions } from '../../hooks/usePermissions';
import { PermissionGate } from '../ProtectedRoute';
import { useAuth } from '../../contexts/AuthContext';
import { filterProjectsByRole, canPerformAction } from '../../utils/roleBasedFiltering';
import apiService from '../../services/api';

const ProjectList = ({ onCreateProject, onEditProject, onViewProject }) => {
  const [projects, setProjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [priorityFilter, setPriorityFilter] = useState('all');
  const [viewMode, setViewMode] = useState('grid'); // grid or list
  const permissions = usePermissions();
  const { user, userRole } = useAuth();

  useEffect(() => {
    loadProjects();
  }, [statusFilter, priorityFilter]);

  const loadProjects = async () => {
    try {
      setLoading(true);
      const params = {};
      if (statusFilter !== 'all') params.status = statusFilter;
      if (priorityFilter !== 'all') params.priority = priorityFilter;
      if (searchTerm) params.search = searchTerm;

      const data = await apiService.getProjects(params);
      setProjects(data.projects || []);
    } catch (error) {
      console.error('Error loading projects:', error);
      setProjects([]);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
    // Debounce search
    clearTimeout(window.searchTimeout);
    window.searchTimeout = setTimeout(() => {
      loadProjects();
    }, 500);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-100 border-green-200';
      case 'in-progress': return 'text-blue-600 bg-blue-100 border-blue-200';
      case 'planning': return 'text-yellow-600 bg-yellow-100 border-yellow-200';
      case 'on-hold': return 'text-red-600 bg-red-100 border-red-200';
      default: return 'text-gray-600 bg-gray-100 border-gray-200';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return 'border-l-red-500';
      case 'medium': return 'border-l-yellow-500';
      case 'low': return 'border-l-green-500';
      default: return 'border-l-gray-500';
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'completed': return <CheckCircle size={16} className="text-green-600" />;
      case 'in-progress': return <Clock size={16} className="text-blue-600" />;
      case 'planning': return <Target size={16} className="text-yellow-600" />;
      case 'on-hold': return <XCircle size={16} className="text-red-600" />;
      default: return <AlertCircle size={16} className="text-gray-600" />;
    }
  };

  // Apply role-based filtering first
  const roleFilteredProjects = filterProjectsByRole(
    projects,
    userRole,
    user?.id,
    user?.department,
    user?.team_ids || []
  );

  const filteredProjects = roleFilteredProjects.filter(project => {
    const matchesSearch = !searchTerm ||
      project.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      project.description?.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesSearch;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-agno-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header and Filters */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Projects</h2>
          <p className="text-gray-600 mt-1">Manage and track all your projects</p>
        </div>
        
        <PermissionGate permission="projectKanbanBoards">
          <button
            onClick={onCreateProject}
            className="flex items-center px-4 py-2 agno-bg-primary text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus size={16} className="mr-2" />
            New Project
          </button>
        </PermissionGate>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
          <input
            type="text"
            placeholder="Search projects..."
            value={searchTerm}
            onChange={handleSearch}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        
        <div className="flex gap-3">
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">All Status</option>
            <option value="planning">Planning</option>
            <option value="in-progress">In Progress</option>
            <option value="completed">Completed</option>
            <option value="on-hold">On Hold</option>
          </select>
          
          <select
            value={priorityFilter}
            onChange={(e) => setPriorityFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">All Priority</option>
            <option value="high">High</option>
            <option value="medium">Medium</option>
            <option value="low">Low</option>
          </select>
          
          <button className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
            <Filter size={16} />
          </button>
        </div>
      </div>

      {/* Projects Grid */}
      {filteredProjects.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredProjects.map((project) => (
            <div
              key={project.id}
              className={`bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow ${getPriorityColor(project.priority)} border-l-4`}
            >
              {/* Project Header */}
              <div className="flex justify-between items-start mb-4">
                <div className="flex-1">
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">
                    {project.name || 'Untitled Project'}
                  </h3>
                  <p className="text-gray-600 text-sm line-clamp-2">
                    {project.description || 'No description available'}
                  </p>
                </div>
                
                <div className="flex items-center space-x-2 ml-4">
                  {canPerformAction('view', 'project', project, userRole, user?.id) && (
                    <button
                      onClick={() => onViewProject?.(project)}
                      className="text-gray-400 hover:text-blue-600 transition-colors"
                    >
                      <Eye size={16} />
                    </button>
                  )}
                  {canPerformAction('edit', 'project', project, userRole, user?.id) && (
                    <PermissionGate permission="projectKanbanBoards">
                      <button
                        onClick={() => onEditProject?.(project)}
                        className="text-gray-400 hover:text-green-600 transition-colors"
                      >
                        <Edit size={16} />
                      </button>
                    </PermissionGate>
                  )}
                  <button className="text-gray-400 hover:text-gray-600 transition-colors">
                    <MoreVertical size={16} />
                  </button>
                </div>
              </div>

              {/* Project Stats */}
              <div className="space-y-3 mb-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-500">Status</span>
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(project.status)}
                    <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(project.status)}`}>
                      {project.status?.replace('-', ' ') || 'Unknown'}
                    </span>
                  </div>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-500">Progress</span>
                  <span className="text-sm font-medium">{project.progress || 0}%</span>
                </div>
                
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                    style={{ width: `${project.progress || 0}%` }}
                  ></div>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-500">Tasks</span>
                  <span className="text-sm font-medium">
                    {project.tasksCompleted || 0}/{project.tasksTotal || 0}
                  </span>
                </div>
              </div>

              {/* Project Footer */}
              <div className="flex justify-between items-center pt-4 border-t border-gray-200">
                <div className="flex items-center space-x-2">
                  <Calendar size={14} className="text-gray-400" />
                  <span className="text-xs text-gray-500">
                    {project.start_date ? new Date(project.start_date).toLocaleDateString() : 'No date'}
                  </span>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Users size={14} className="text-gray-400" />
                  <div className="flex -space-x-2">
                    {project.teamMembers?.slice(0, 3).map((member, index) => (
                      <div
                        key={index}
                        className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs border-2 border-white"
                        title={member}
                      >
                        {member.split(' ').map(n => n[0]).join('')}
                      </div>
                    )) || (
                      <div className="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center text-gray-600 text-xs">
                        ?
                      </div>
                    )}
                    {project.teamMembers?.length > 3 && (
                      <div className="w-6 h-6 bg-gray-500 rounded-full flex items-center justify-center text-white text-xs border-2 border-white">
                        +{project.teamMembers.length - 3}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <Target size={48} className="mx-auto text-gray-300 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No projects found</h3>
          <p className="text-gray-500 mb-4">
            {searchTerm || statusFilter !== 'all' || priorityFilter !== 'all'
              ? 'Try adjusting your search or filters'
              : 'Get started by creating your first project'
            }
          </p>
          <PermissionGate permission="projectKanbanBoards">
            <button
              onClick={onCreateProject}
              className="inline-flex items-center px-4 py-2 agno-bg-primary text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Plus size={16} className="mr-2" />
              Create Project
            </button>
          </PermissionGate>
        </div>
      )}
    </div>
  );
};

export default ProjectList;
