import { useState, useRef, useEffect } from "react";
import { Camera, Upload, MapPin, Clock, Activity } from "lucide-react";
import { defaultImages } from "../utils/defaultImages";
import apiService from "../services/api";
import { useAuth } from "../contexts/AuthContext";

export default function ProfileSection() {
  const { isAuthenticated, login } = useAuth();
  const [isCheckedIn, setIsCheckedIn] = useState(false);
  const [checkInTime, setCheckInTime] = useState(null);
  const [elapsedTime, setElapsedTime] = useState("00:00:00");
  const [profileImage, setProfileImage] = useState(defaultImages.avatar);
  const [loading, setLoading] = useState(false);
  const [location, setLocation] = useState(null);
  const [attendanceRecord, setAttendanceRecord] = useState(null);
  const [timesheetActive, setTimesheetActive] = useState(false);
  const [authError, setAuthError] = useState(false);
  const fileInputRef = useRef(null);
  const timerRef = useRef(null);

  // Initialize component - check current attendance status
  useEffect(() => {
    if (isAuthenticated) {
      checkCurrentAttendanceStatus();
      getCurrentLocation();
    } else {
      // Auto-login with demo user for testing
      handleDemoLogin();
    }
  }, [isAuthenticated]);

  // Auto-login with demo user for testing
  const handleDemoLogin = async () => {
    try {
      await login({
        email: '<EMAIL>',
        password: 'password123'
      });
      setAuthError(false);
    } catch (error) {
      console.error('Demo login failed:', error);
      setAuthError(true);
    }
  };

  // Update elapsed time
  const updateElapsedTime = () => {
    if (!checkInTime) return;

    const now = new Date();
    const diff = now - checkInTime;
    const hours = Math.floor(diff / (1000 * 60 * 60)).toString().padStart(2, "0");
    const minutes = Math.floor((diff / (1000 * 60)) % 60).toString().padStart(2, "0");
    const seconds = Math.floor((diff / 1000) % 60).toString().padStart(2, "0");

    setElapsedTime(`${hours}:${minutes}:${seconds}`);
  };

  // Check current attendance status on component load
  const checkCurrentAttendanceStatus = async () => {
    try {
      const response = await apiService.get('/attendance/my/today');
      if (response && response.check_in_time && !response.check_out_time) {
        setIsCheckedIn(true);
        setCheckInTime(new Date(response.check_in_time));
        setAttendanceRecord(response);
        startTimer();
      }
    } catch (error) {
      console.log('No current attendance record found');
    }
  };

  // Get current location
  const getCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          setLocation({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude
          });
        },
        (error) => {
          console.log('Location access denied:', error);
        }
      );
    }
  };

  // Start timer
  const startTimer = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }
    timerRef.current = setInterval(() => {
      updateElapsedTime();
    }, 1000);
  };

  // Stop timer
  const stopTimer = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  };

  // Start timesheet tracking
  const startTimesheetTracking = async () => {
    try {
      const timesheetData = {
        project_name: 'Daily Work',
        task_name: 'General Tasks',
        start_time: new Date().toISOString(),
        description: 'Daily work session started'
      };

      await apiService.post('/timesheet/', timesheetData);
      setTimesheetActive(true);
      console.log('📊 Timesheet tracking started');
    } catch (error) {
      console.error('Failed to start timesheet tracking:', error);
    }
  };

  // Stop timesheet tracking
  const stopTimesheetTracking = async () => {
    try {
      if (timesheetActive) {
        // You would typically update the existing timesheet entry with end time
        // For now, we'll just mark it as inactive
        setTimesheetActive(false);
        console.log('📊 Timesheet tracking stopped');
      }
    } catch (error) {
      console.error('Failed to stop timesheet tracking:', error);
    }
  };

  // Show notification
  const showNotification = (message, type = 'info') => {
    // Create a simple notification
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg text-white ${
      type === 'success' ? 'bg-green-500' :
      type === 'error' ? 'bg-red-500' : 'bg-blue-500'
    }`;
    notification.textContent = message;

    document.body.appendChild(notification);

    // Remove after 3 seconds
    setTimeout(() => {
      document.body.removeChild(notification);
    }, 3000);
  };

  // Handle profile image upload
  const handleImageUpload = (event) => {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setProfileImage(e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopTimer();
    };
  }, []);

  // Enhanced check-in/out handler with full integration
  const handleCheckInOut = async () => {
    if (loading) return;

    // Check authentication first
    if (!isAuthenticated) {
      showNotification('⚠️ Please wait, logging in...', 'info');
      await handleDemoLogin();
      return;
    }

    setLoading(true);
    try {
      if (isCheckedIn) {
        // Check out - stop all tracking
        await handleCheckOut();
      } else {
        // Check in - start all tracking
        await handleCheckIn();
      }
    } catch (error) {
      console.error('Check-in/out error:', error);

      // Handle authentication errors
      if (error.response?.status === 401) {
        showNotification('🔐 Session expired. Logging in again...', 'info');
        await handleDemoLogin();
        // Retry the operation after login
        setTimeout(() => {
          handleCheckInOut();
        }, 1000);
      } else {
        showNotification('❌ Failed to process check-in/out. Please try again.', 'error');
      }
    } finally {
      setLoading(false);
    }
  };

  // Handle check-in with full integration
  const handleCheckIn = async () => {
    try {
      // Prepare check-in data
      const checkInData = {
        work_location: 'Office', // You can make this dynamic
        is_remote: false,
        latitude: location?.latitude,
        longitude: location?.longitude,
        location_name: 'Office Location', // You can get this from reverse geocoding
        photo_url: null // You can add photo capture functionality
      };

      // Call attendance API
      const response = await apiService.post('/attendance/my/check-in', checkInData);

      if (response) {
        // Update local state
        setIsCheckedIn(true);
        setCheckInTime(new Date());
        setAttendanceRecord(response);

        // Start timer
        startTimer();

        // Start timesheet tracking
        await startTimesheetTracking();

        // Show success notification
        showNotification('✅ Checked in successfully! Timer and tracking started.', 'success');

        console.log('✅ Check-in successful:', {
          attendance: 'Started',
          timer: 'Started',
          timesheet: 'Started',
          location: location ? 'Captured' : 'Not available'
        });
      }
    } catch (error) {
      console.error('Check-in failed:', error);
      throw error;
    }
  };

  // Handle check-out with full integration
  const handleCheckOut = async () => {
    try {
      // Prepare check-out data
      const checkOutData = {
        latitude: location?.latitude,
        longitude: location?.longitude,
        location_name: 'Office Location',
        notes: 'Regular check-out'
      };

      // Call attendance API
      await apiService.post('/attendance/my/check-out', checkOutData);

      // Update local state
      setIsCheckedIn(false);
      setCheckInTime(null);
      setElapsedTime("00:00:00");
      setAttendanceRecord(null);

      // Stop timer
      stopTimer();

      // Stop timesheet tracking
      await stopTimesheetTracking();

      // Show success notification
      showNotification('✅ Checked out successfully! All tracking stopped.', 'success');

      console.log('✅ Check-out successful:', {
        attendance: 'Stopped',
        timer: 'Stopped',
        timesheet: 'Stopped'
      });
    } catch (error) {
      console.error('Check-out failed:', error);
      throw error;
    }
  };

  return (
    <div className="bg-white rounded-lg shadow-sm p-4">
      <div className="flex justify-center mb-4">
        <div className="relative w-20 h-20 rounded-lg overflow-hidden group">
          <img
            src={profileImage}
            alt="Profile"
            className="w-full h-full object-cover"
          />

          {/* Upload overlay */}
          <div
            className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity cursor-pointer"
            onClick={() => fileInputRef.current?.click()}
          >
            <Camera className="text-white" size={20} />
          </div>

          {/* Hidden file input */}
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleImageUpload}
            className="hidden"
          />
        </div>
      </div>
      
      <div className="text-center mb-4">
        <div className={`font-medium flex items-center justify-center gap-2 ${isCheckedIn ? "text-green-500" : "text-red-500"}`}>
          <Activity size={16} className={isCheckedIn ? "animate-pulse" : ""} />
          {isCheckedIn ? "Checked In" : "Checked Out"}
        </div>
        <div className="text-2xl font-bold mt-1">
          {elapsedTime}
        </div>

        {/* Status indicators */}
        {isCheckedIn && (
          <div className="mt-2 space-y-1">
            <div className="flex items-center justify-center gap-2 text-xs text-green-600">
              <Clock size={12} />
              <span>Timer Active</span>
            </div>
            {timesheetActive && (
              <div className="flex items-center justify-center gap-2 text-xs text-blue-600">
                <Activity size={12} />
                <span>Timesheet Tracking</span>
              </div>
            )}
            {location && (
              <div className="flex items-center justify-center gap-2 text-xs text-purple-600">
                <MapPin size={12} />
                <span>Location Tracked</span>
              </div>
            )}
          </div>
        )}
      </div>
      
      <div className="flex justify-center mb-6">
        <button
          className={`px-6 py-3 border rounded-lg transition-all duration-200 font-medium flex items-center gap-2 ${
            loading || !isAuthenticated
              ? "bg-gray-400 text-white cursor-not-allowed"
              : isCheckedIn
              ? "border-red-500 text-red-500 hover:bg-red-50 hover:shadow-md"
              : "agno-bg-orange text-white hover:bg-orange-600 border-transparent hover:shadow-md"
          }`}
          onClick={handleCheckInOut}
          disabled={loading || !isAuthenticated}
        >
          {loading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <span>Processing...</span>
            </>
          ) : !isAuthenticated ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <span>Authenticating...</span>
            </>
          ) : (
            <>
              {isCheckedIn ? (
                <>
                  <Clock size={16} />
                  <span>Check-out</span>
                </>
              ) : (
                <>
                  <Activity size={16} />
                  <span>Check-in</span>
                </>
              )}
            </>
          )}
        </button>
      </div>
      
      <div className="border-t pt-4">
        <h3 className="font-medium mb-3">Reportees</h3>
        
        <div className="space-y-4">
          <ReporteeItem 
            id="S19" 
            name="Michael Johnson" 
            status="yet-to-check-in"
          />
          
          <ReporteeItem 
            id="S2" 
            name="Lily Williams" 
            status="yet-to-check-in"
          />
          
          <ReporteeItem 
            id="S20" 
            name="Christopher Brown" 
            status="yet-to-check-in"
          />
          
          <div className="text-blue-600 text-sm font-medium cursor-pointer hover:underline">
            +1 More
          </div>
        </div>
      </div>
    </div>
  );
}

// Reportee Item Component
function ReporteeItem({ id, name, status }) {
  const getStatusText = (status) => {
    switch (status) {
      case "checked-in": return "Checked in";
      case "checked-out": return "Checked out";
      case "on-leave": return "On leave";
      case "yet-to-check-in": 
      default: return "Yet to check-in";
    }
  };
  
  const getStatusColor = (status) => {
    switch (status) {
      case "checked-in": return "text-green-500";
      case "checked-out": return "text-blue-500";
      case "on-leave": return "text-orange-500";
      case "yet-to-check-in": 
      default: return "text-red-500";
    }
  };
  
  return (
    <div className="flex items-center gap-3">
      <div className="w-8 h-8 rounded-full overflow-hidden flex-shrink-0 bg-gradient-to-br from-gray-500 to-gray-700 flex items-center justify-center text-white text-xs font-medium">
        {name.split(" ").map(n => n[0]).join("")}
      </div>
      <div>
        <div className="flex items-center gap-2">
          <span className="text-gray-500 text-sm">{id}</span>
          <span className="font-medium">{name}</span>
        </div>
        <div className={`text-xs ${getStatusColor(status)}`}>
          {getStatusText(status)}
        </div>
      </div>
    </div>
  );
}