/**
 * Custom hook for project data management
 * Handles loading states, error handling, and data caching
 */

import { useState, useEffect, useCallback } from 'react';
import apiService from '../services/api';

export const useProjects = (filters = {}) => {
  const [projects, setProjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [pagination, setPagination] = useState({ total: 0, skip: 0, limit: 20 });

  const loadProjects = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await apiService.getProjects({ ...filters, ...pagination });
      setProjects(data.projects || []);
      setPagination(prev => ({ ...prev, total: data.total || 0 }));
    } catch (err) {
      setError(err.message || 'Failed to load projects');
      console.error('Error loading projects:', err);
    } finally {
      setLoading(false);
    }
  }, [filters, pagination.skip, pagination.limit]);

  useEffect(() => {
    loadProjects();
  }, [loadProjects]);

  const createProject = async (projectData) => {
    try {
      const newProject = await apiService.createProject(projectData);
      setProjects(prev => [newProject, ...prev]);
      return newProject;
    } catch (err) {
      setError(err.message || 'Failed to create project');
      throw err;
    }
  };

  const updateProject = async (projectId, projectData) => {
    try {
      const updatedProject = await apiService.updateProject(projectId, projectData);
      setProjects(prev => prev.map(p => p.id === projectId ? updatedProject : p));
      return updatedProject;
    } catch (err) {
      setError(err.message || 'Failed to update project');
      throw err;
    }
  };

  const deleteProject = async (projectId) => {
    try {
      await apiService.deleteProject(projectId);
      setProjects(prev => prev.filter(p => p.id !== projectId));
    } catch (err) {
      setError(err.message || 'Failed to delete project');
      throw err;
    }
  };

  const refreshProjects = () => {
    loadProjects();
  };

  return {
    projects,
    loading,
    error,
    pagination,
    createProject,
    updateProject,
    deleteProject,
    refreshProjects,
    setError
  };
};

export const useTasks = (projectId = null, filters = {}) => {
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const loadTasks = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      let data;
      if (projectId) {
        data = await apiService.getProjectTasks(projectId, filters);
      } else {
        data = await apiService.getTasks(filters);
      }
      setTasks(data.tasks || []);
    } catch (err) {
      setError(err.message || 'Failed to load tasks');
      console.error('Error loading tasks:', err);
    } finally {
      setLoading(false);
    }
  }, [projectId, filters]);

  useEffect(() => {
    loadTasks();
  }, [loadTasks]);

  const createTask = async (taskData) => {
    try {
      const newTask = await apiService.createTask(taskData);
      setTasks(prev => [newTask, ...prev]);
      return newTask;
    } catch (err) {
      setError(err.message || 'Failed to create task');
      throw err;
    }
  };

  const updateTask = async (taskId, taskData) => {
    try {
      const updatedTask = await apiService.updateTask(taskId, taskData);
      setTasks(prev => prev.map(t => t.id === taskId ? updatedTask : t));
      return updatedTask;
    } catch (err) {
      setError(err.message || 'Failed to update task');
      throw err;
    }
  };

  const updateTaskStatus = async (taskId, status) => {
    try {
      const updatedTask = await apiService.updateTaskStatus(taskId, status);
      setTasks(prev => prev.map(t => t.id === taskId ? { ...t, status } : t));
      return updatedTask;
    } catch (err) {
      setError(err.message || 'Failed to update task status');
      throw err;
    }
  };

  const deleteTask = async (taskId) => {
    try {
      await apiService.deleteTask(taskId);
      setTasks(prev => prev.filter(t => t.id !== taskId));
    } catch (err) {
      setError(err.message || 'Failed to delete task');
      throw err;
    }
  };

  const refreshTasks = () => {
    loadTasks();
  };

  return {
    tasks,
    loading,
    error,
    createTask,
    updateTask,
    updateTaskStatus,
    deleteTask,
    refreshTasks,
    setError
  };
};

export const useProjectDashboard = (projectId = null) => {
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const loadDashboard = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await apiService.getProjectDashboard(projectId);
      setDashboardData(data);
    } catch (err) {
      setError(err.message || 'Failed to load dashboard data');
      console.error('Error loading dashboard:', err);
    } finally {
      setLoading(false);
    }
  }, [projectId]);

  useEffect(() => {
    loadDashboard();
  }, [loadDashboard]);

  const refreshDashboard = () => {
    loadDashboard();
  };

  return {
    dashboardData,
    loading,
    error,
    refreshDashboard,
    setError
  };
};

export const useProjectAnalytics = (projectId = null, timeRange = '30') => {
  const [analyticsData, setAnalyticsData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const loadAnalytics = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await apiService.getProjectAnalytics(projectId, timeRange);
      setAnalyticsData(data);
    } catch (err) {
      setError(err.message || 'Failed to load analytics data');
      console.error('Error loading analytics:', err);
    } finally {
      setLoading(false);
    }
  }, [projectId, timeRange]);

  useEffect(() => {
    loadAnalytics();
  }, [loadAnalytics]);

  const refreshAnalytics = () => {
    loadAnalytics();
  };

  return {
    analyticsData,
    loading,
    error,
    refreshAnalytics,
    setError
  };
};

export const useProjectActivities = (projectId = null, limit = 20) => {
  const [activities, setActivities] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const loadActivities = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await apiService.getProjectActivities(projectId, limit);
      setActivities(data.activities || []);
    } catch (err) {
      setError(err.message || 'Failed to load activities');
      console.error('Error loading activities:', err);
    } finally {
      setLoading(false);
    }
  }, [projectId, limit]);

  useEffect(() => {
    loadActivities();
  }, [loadActivities]);

  const addActivity = (activity) => {
    setActivities(prev => [activity, ...prev].slice(0, limit));
  };

  const refreshActivities = () => {
    loadActivities();
  };

  return {
    activities,
    loading,
    error,
    addActivity,
    refreshActivities,
    setError
  };
};

// WebSocket hook for real-time updates
export const useWebSocket = (url, onMessage, dependencies = []) => {
  const [connected, setConnected] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!url) return;

    let ws;
    let reconnectTimeout;

    const connect = () => {
      try {
        ws = new WebSocket(url);
        
        ws.onopen = () => {
          setConnected(true);
          setError(null);
          console.log('WebSocket connected:', url);
        };
        
        ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            onMessage?.(data);
          } catch (err) {
            console.error('Error parsing WebSocket message:', err);
          }
        };
        
        ws.onclose = () => {
          setConnected(false);
          console.log('WebSocket disconnected:', url);
          
          // Attempt to reconnect after 5 seconds
          reconnectTimeout = setTimeout(() => {
            connect();
          }, 5000);
        };
        
        ws.onerror = (err) => {
          setError('WebSocket connection error');
          console.error('WebSocket error:', err);
        };
      } catch (err) {
        setError('Failed to create WebSocket connection');
        console.error('WebSocket creation error:', err);
      }
    };

    connect();

    return () => {
      if (reconnectTimeout) {
        clearTimeout(reconnectTimeout);
      }
      if (ws) {
        ws.close();
      }
    };
  }, [url, ...dependencies]);

  return { connected, error };
};
