/**
 * Enhanced Employees Page with Premium UI/UX
 * Features: Dashboard, Directory, Profile Management with Professional Design
 */

import React, { useState, useEffect } from 'react';
import { Search, Filter, Plus, Edit, Eye, MoreVertical, Users, BarChart3, User, TrendingUp } from 'lucide-react';
import { usePermissions } from '../hooks/usePermissions';
import { PermissionGate, ConditionalRender } from '../components/ProtectedRoute';
import apiService from '../services/api';

// Import enhanced employee components
import EmployeeDashboard from '../components/employee/EmployeeDashboard';
import EmployeeDirectory from '../components/employee/EmployeeDirectory';
import EmployeeProfile from '../components/employee/EmployeeProfile';
import EmployeeForm from '../components/employee/EmployeeForm';
import EmployeeAnalytics from '../components/employee/EmployeeAnalytics';

export default function Employees() {
  const [currentTab, setCurrentTab] = useState('dashboard');
  const [selectedEmployee, setSelectedEmployee] = useState(null);
  const [showEmployeeForm, setShowEmployeeForm] = useState(false);
  const [editingEmployee, setEditingEmployee] = useState(null);
  const permissions = usePermissions();

  // Tab configuration with enhanced navigation
  const tabs = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: BarChart3,
      description: 'Overview and analytics'
    },
    {
      id: 'directory',
      label: 'Directory',
      icon: Users,
      description: 'Employee listing'
    },
    {
      id: 'profiles',
      label: 'Profiles',
      icon: User,
      description: 'Profile management'
    },
    {
      id: 'analytics',
      label: 'Analytics',
      icon: TrendingUp,
      description: 'Reports and insights'
    }
  ];

  const handleEmployeeSelect = (employee) => {
    setSelectedEmployee(employee);
    setCurrentTab('profiles');
  };

  const handleEmployeeEdit = (employee) => {
    setEditingEmployee(employee);
    setShowEmployeeForm(true);
  };

  const handleEmployeeAdd = () => {
    setEditingEmployee(null);
    setShowEmployeeForm(true);
  };

  const handleFormSave = (employeeData) => {
    // Handle save logic here
    setShowEmployeeForm(false);
    setEditingEmployee(null);
    // Refresh data if needed
  };

  const handleFormCancel = () => {
    setShowEmployeeForm(false);
    setEditingEmployee(null);
  };

  // Enhanced Tab Navigation Component
  const TabNavigation = () => (
    <div className="bg-white rounded-xl shadow-agno border border-gray-100 mb-8">
      <div className="flex overflow-x-auto">
        {tabs.map((tab) => {
          const Icon = tab.icon;
          return (
            <button
              key={tab.id}
              onClick={() => setCurrentTab(tab.id)}
              className={`flex items-center space-x-3 px-6 py-4 text-sm font-medium border-b-2 transition-all duration-200 whitespace-nowrap ${
                currentTab === tab.id
                  ? 'border-agno-primary text-agno-primary bg-blue-50'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 hover:bg-gray-50'
              }`}
            >
              <Icon size={18} />
              <div className="text-left">
                <div className="font-semibold">{tab.label}</div>
                <div className="text-xs text-gray-400">{tab.description}</div>
              </div>
            </button>
          );
        })}
      </div>
    </div>
  );

  // Content rendering based on active tab
  const renderContent = () => {
    switch (currentTab) {
      case 'dashboard':
        return <EmployeeDashboard />;
      case 'directory':
        return (
          <EmployeeDirectory
            onEmployeeSelect={handleEmployeeSelect}
            onEmployeeEdit={handleEmployeeEdit}
            onEmployeeAdd={handleEmployeeAdd}
          />
        );
      case 'profiles':
        return selectedEmployee ? (
          <EmployeeProfile
            employeeId={selectedEmployee.id}
            onClose={() => {
              setSelectedEmployee(null);
              setCurrentTab('directory');
            }}
          />
        ) : (
          <div className="bg-white rounded-xl shadow-agno border border-gray-100 p-8 text-center">
            <User className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No Employee Selected</h3>
            <p className="text-gray-600 mb-4">Select an employee from the directory to view their profile</p>
            <button
              onClick={() => setCurrentTab('directory')}
              className="btn-agno-primary px-4 py-2 rounded-lg"
            >
              Go to Directory
            </button>
          </div>
        );
      case 'analytics':
        return <EmployeeAnalytics />;
      default:
        return <EmployeeDashboard />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="p-6 space-y-8">
        {/* Page Header */}
        <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Employee Management</h1>
            <p className="text-gray-600">Comprehensive employee management with premium features</p>
          </div>

          <PermissionGate permission="employeeDirectory">
            <button
              onClick={handleEmployeeAdd}
              className="btn-agno-primary flex items-center space-x-2 px-6 py-3 rounded-lg"
            >
              <Plus size={16} />
              <span>Add Employee</span>
            </button>
          </PermissionGate>
        </div>

        {/* Tab Navigation */}
        <TabNavigation />

        {/* Main Content */}
        <div className="fade-in">
          {renderContent()}
        </div>

        {/* Employee Form Modal */}
        {showEmployeeForm && (
          <EmployeeForm
            employee={editingEmployee}
            onSave={handleFormSave}
            onCancel={handleFormCancel}
            isModal={true}
          />
        )}
      </div>
    </div>
  );
}


