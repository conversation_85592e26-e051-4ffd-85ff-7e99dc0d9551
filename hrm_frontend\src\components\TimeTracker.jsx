/**
 * Enhanced Time Tracker Component with Backend Integration
 * Provides real-time timer, check-in/out functionality, and attendance tracking
 */

import React, { useState, useEffect } from 'react';
import { Play, Pause, Clock, MapPin, Calendar, CheckCircle, AlertCircle, Loader } from 'lucide-react';
import { usePermissions } from '../hooks/usePermissions';
import { PermissionGate } from './ProtectedRoute';
import apiService from '../services/api';

export default function TimeTracker({ className = '' }) {
  const permissions = usePermissions();
  const [isCheckedIn, setIsCheckedIn] = useState(false);
  const [workTimer, setWorkTimer] = useState(0);
  const [checkInTime, setCheckInTime] = useState(null);
  const [checkOutTime, setCheckOutTime] = useState(null);
  const [notes, setNotes] = useState('');
  const [location, setLocation] = useState('Office');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [attendanceStatus, setAttendanceStatus] = useState(null);
  const [currentUser, setCurrentUser] = useState(null);
  const [isOnBreak, setIsOnBreak] = useState(false);
  const [breakStartTime, setBreakStartTime] = useState(null);

  // Initialize component and load current status
  useEffect(() => {
    console.log('🔧 TimeTracker component mounted');
    loadCurrentStatus();

    // For immediate testing: Start a basic timer
    console.log('🔧 Setting up test timer...');
    setIsCheckedIn(true);
    setWorkTimer(0);
    setCheckInTime(new Date().toISOString());
  }, []);

  // Real-time timer effect
  useEffect(() => {
    console.log('🔧 Timer effect triggered:', { isCheckedIn, isOnBreak });
    let interval;
    if (isCheckedIn && !isOnBreak) {
      console.log('✅ Starting timer interval');
      interval = setInterval(() => {
        setWorkTimer(prev => {
          const newValue = prev + 1;
          console.log('⏱️ Timer tick:', newValue);
          return newValue;
        });
      }, 1000);
    } else {
      console.log('❌ Timer not starting:', { isCheckedIn, isOnBreak });
    }
    return () => {
      if (interval) {
        console.log('🛑 Clearing timer interval');
        clearInterval(interval);
      }
    };
  }, [isCheckedIn, isOnBreak]);

  // Load current attendance status
  const loadCurrentStatus = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('🔄 Loading current attendance status...');

      // Get employee info
      const employeeInfo = await apiService.getMyEmployeeInfo();
      console.log('👤 Employee info:', employeeInfo);

      if (!employeeInfo || !employeeInfo.id) {
        console.error('❌ No employee info found');
        setError('Employee information not found. Please log in again.');
        return;
      }

      setCurrentUser(employeeInfo);

      // Get attendance status
      const status = await apiService.getMyAttendanceStatus();
      console.log('📊 Attendance status:', status);
      setAttendanceStatus(status);

      if (status && status.is_checked_in) {
        console.log('✅ User is checked in, setting up timer...');
        setIsCheckedIn(true);
        setCheckInTime(status.check_in_time);

        // Calculate elapsed time if checked in
        if (status.check_in_time) {
          const checkInDate = new Date(status.check_in_time);
          const now = new Date();
          const elapsedSeconds = Math.floor((now - checkInDate) / 1000);
          console.log(`⏱️ Calculated elapsed time: ${elapsedSeconds} seconds`);
          setWorkTimer(elapsedSeconds);
        }

        // Check if on break
        if (status.is_on_break) {
          console.log('☕ User is on break');
          setIsOnBreak(true);
          setBreakStartTime(status.break_start_time);
        }
      } else {
        console.log('❌ User is not checked in');
        setIsCheckedIn(false);
        setWorkTimer(0);
      }

    } catch (error) {
      console.error('❌ Error loading attendance status:', error);
      setError(`Failed to load attendance status: ${error.message}`);

      // Fallback: Check localStorage for any cached state
      try {
        const cachedState = localStorage.getItem('hrm_timer_state');
        if (cachedState) {
          const state = JSON.parse(cachedState);
          console.log('🔄 Using cached timer state:', state);
          setIsCheckedIn(state.isCheckedIn || false);
          setCheckInTime(state.checkInTime);
          if (state.checkInTime && state.isCheckedIn) {
            const checkInDate = new Date(state.checkInTime);
            const now = new Date();
            const elapsedSeconds = Math.floor((now - checkInDate) / 1000);
            setWorkTimer(elapsedSeconds);
          }
        }
      } catch (cacheError) {
        console.error('❌ Error loading cached state:', cacheError);
      }
    } finally {
      setLoading(false);
    }
  };

  // Save timer state to localStorage
  const saveTimerState = (state) => {
    try {
      localStorage.setItem('hrm_timer_state', JSON.stringify(state));
    } catch (error) {
      console.error('Error saving timer state:', error);
    }
  };

  // Format timer display
  const formatTimer = (seconds) => {
    const hrs = String(Math.floor(seconds / 3600)).padStart(2, '0');
    const mins = String(Math.floor((seconds % 3600) / 60)).padStart(2, '0');
    const secs = String(seconds % 60).padStart(2, '0');
    return `${hrs}:${mins}:${secs}`;
  };

  // Get current time string
  const getCurrentTime = () => {
    return new Date().toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  // Handle check-in
  const handleCheckIn = async () => {
    if (!currentUser || !currentUser.id) {
      setError('Employee information not found. Please log in again.');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const checkInData = {
        work_location: location,
        is_remote: location === 'Remote',
        notes: notes || 'Check-in via time tracker',
        latitude: null, // Can be enhanced with geolocation
        longitude: null
      };

      console.log('🔄 Checking in with data:', checkInData);

      const response = await apiService.checkIn(currentUser.id, checkInData);

      console.log('✅ Check-in successful:', response);

      // Update UI state
      const currentTime = getCurrentTime();
      const now = new Date().toISOString();
      setIsCheckedIn(true);
      setCheckInTime(now);
      setWorkTimer(0);
      setCheckOutTime(null);
      setIsOnBreak(false);

      // Save state to localStorage
      saveTimerState({
        isCheckedIn: true,
        checkInTime: now,
        checkOutTime: null,
        isOnBreak: false
      });

      console.log('✅ Timer started and state saved');

      // Refresh status
      await loadCurrentStatus();

    } catch (error) {
      console.error('❌ Check-in failed:', error);
      setError(error.message || 'Failed to check in. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle check-out
  const handleCheckOut = async () => {
    if (!currentUser || !currentUser.id) {
      setError('Employee information not found. Please log in again.');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const checkOutData = {
        notes: notes || 'Check-out via time tracker',
        work_summary: `Worked for ${formatTimer(workTimer)}`
      };

      console.log('🔄 Checking out with data:', checkOutData);

      const response = await apiService.checkOut(currentUser.id, checkOutData);

      console.log('✅ Check-out successful:', response);

      // Update UI state
      const currentTime = getCurrentTime();
      setIsCheckedIn(false);
      setCheckOutTime(currentTime);
      setIsOnBreak(false);
      setBreakStartTime(null);

      // Clear saved state
      saveTimerState({
        isCheckedIn: false,
        checkInTime: null,
        checkOutTime: currentTime,
        isOnBreak: false
      });

      console.log('✅ Timer stopped and state cleared');

      // Refresh status
      await loadCurrentStatus();

    } catch (error) {
      console.error('❌ Check-out failed:', error);
      setError(error.message || 'Failed to check out. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  // Handle break management
  const handleBreak = async () => {
    if (!currentUser || !currentUser.id) {
      setError('Employee information not found. Please log in again.');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const breakData = {
        break_type: isOnBreak ? 'end' : 'start',
        notes: isOnBreak ? 'Break ended' : 'Break started'
      };

      const response = await apiService.manageBreak(currentUser.id, breakData);

      // Update UI state
      setIsOnBreak(!isOnBreak);
      if (!isOnBreak) {
        setBreakStartTime(new Date().toISOString());
      } else {
        setBreakStartTime(null);
      }

      // Refresh status
      await loadCurrentStatus();

    } catch (error) {
      console.error('❌ Break management failed:', error);
      setError(error.message || 'Failed to manage break. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    // <PermissionGate permission="timeTracker">
      <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <Clock className="agno-text-orange" size={24} />
            <h3 className="text-lg font-semibold text-gray-900">Time Tracker</h3>
          </div>
          
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Calendar size={16} />
            <span>{new Date().toLocaleDateString()}</span>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg flex items-center space-x-2">
            <AlertCircle className="h-4 w-4 text-red-500" />
            <span className="text-sm text-red-700">{error}</span>
            <button
              onClick={() => setError(null)}
              className="ml-auto text-red-500 hover:text-red-700"
            >
              ×
            </button>
          </div>
        )}

        {/* Loading State */}
        {loading && (
          <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg flex items-center space-x-2">
            <Loader className="h-4 w-4 text-blue-500 animate-spin" />
            <span className="text-sm text-blue-700">Processing...</span>
          </div>
        )}

        {/* Debug Info - Remove in production */}
        {process.env.NODE_ENV === 'development' && (
          <div className="mb-4 p-3 bg-gray-50 border border-gray-200 rounded-lg text-xs">
            <div className="font-semibold mb-2">Debug Info:</div>
            <div>isCheckedIn: {isCheckedIn ? 'true' : 'false'}</div>
            <div>workTimer: {workTimer}</div>
            <div>checkInTime: {checkInTime || 'null'}</div>
            <div>isOnBreak: {isOnBreak ? 'true' : 'false'}</div>
            <div>currentUser: {currentUser ? currentUser.id : 'null'}</div>
            <div>attendanceStatus: {attendanceStatus ? JSON.stringify(attendanceStatus) : 'null'}</div>
          </div>
        )}

        {/* Current Status */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <div className={`w-3 h-3 rounded-full ${
                isCheckedIn ? (isOnBreak ? 'bg-yellow-500' : 'bg-green-500') : 'bg-gray-400'
              }`}></div>
              <span className="font-medium text-gray-900">
                {isCheckedIn ? (isOnBreak ? 'On Break' : 'Currently Working') : 'Not Checked In'}
              </span>
              {isOnBreak && breakStartTime && (
                <span className="text-xs text-yellow-600 ml-2">
                  (Break started at {new Date(breakStartTime).toLocaleTimeString()})
                </span>
              )}
            </div>
            
            <div className="text-2xl font-bold agno-text-orange">
              {formatTimer(workTimer)}
              <div className="text-sm text-gray-500">Raw: {workTimer}s</div>
            </div>
          </div>

          {/* Work Schedule Info */}
          <div className="bg-gray-50 rounded-lg p-4 mb-4">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">Today's Schedule:</span>
              <span className="font-medium text-gray-900">General [ 9:00 AM - 6:00 PM ]</span>
            </div>
          </div>
        </div>

        {/* Check-in/Check-out Times */}
        {(checkInTime || checkOutTime) && (
          <div className="grid grid-cols-2 gap-4 mb-6">
            <div className="text-center">
              <div className="text-sm text-gray-600 mb-1">Check In</div>
              <div className="font-semibold text-green-600">
                {checkInTime || '--:--'}
              </div>
            </div>
            <div className="text-center">
              <div className="text-sm text-gray-600 mb-1">Check Out</div>
              <div className="font-semibold text-red-600">
                {checkOutTime || '--:--'}
              </div>
            </div>
          </div>
        )}

        {/* Location Selection */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <MapPin size={16} className="inline mr-1" />
            Work Location
          </label>
          <select
            value={location}
            onChange={(e) => setLocation(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent-500"
          >
            <option value="Office">Office</option>
            <option value="Home">Work from Home</option>
            <option value="Client Site">Client Site</option>
            <option value="Other">Other</option>
          </select>
        </div>

        {/* Notes */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Notes (Optional)
          </label>
          <textarea
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            placeholder="Add notes about your work session..."
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-accent-500 resize-none"
            rows={3}
          />
        </div>

        {/* Action Buttons */}
        <div className="space-y-3">
          {/* Main Check-in/Check-out Button */}
          <button
            onClick={isCheckedIn ? handleCheckOut : handleCheckIn}
            disabled={loading}
            className={`w-full py-3 px-4 rounded-lg font-medium flex items-center justify-center gap-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${
              isCheckedIn
                ? 'bg-red-600 text-white hover:bg-red-700'
                : 'agno-bg-orange text-white hover:bg-accent-600'
            }`}
          >
            {loading ? (
              <>
                <Loader size={20} className="animate-spin" />
                Processing...
              </>
            ) : isCheckedIn ? (
              <>
                <Pause size={20} />
                Check Out
              </>
            ) : (
              <>
                <Play size={20} />
                Check In
              </>
            )}
          </button>

          {/* Break Button - Only show when checked in */}
          {isCheckedIn && (
            <button
              onClick={handleBreak}
              disabled={loading}
              className={`w-full py-2 px-4 rounded-lg font-medium flex items-center justify-center gap-2 transition-colors disabled:opacity-50 disabled:cursor-not-allowed ${
                isOnBreak
                  ? 'bg-green-600 text-white hover:bg-green-700'
                  : 'bg-yellow-600 text-white hover:bg-yellow-700'
              }`}
            >
              {loading ? (
                <>
                  <Loader size={16} className="animate-spin" />
                  Processing...
                </>
              ) : isOnBreak ? (
                <>
                  <Play size={16} />
                  End Break
                </>
              ) : (
                <>
                  <Pause size={16} />
                  Start Break
                </>
              )}
            </button>
          )}

          {/* Debug Controls - Remove in production */}
          {process.env.NODE_ENV === 'development' && (
            <div className="mt-3 space-y-2">
              <button
                onClick={() => {
                  console.log('🔧 Manual timer start');
                  setIsCheckedIn(true);
                  setCheckInTime(new Date().toISOString());
                  setWorkTimer(0);
                }}
                className="w-full py-1 px-3 bg-blue-500 text-white text-sm rounded hover:bg-blue-600"
              >
                🔧 Debug: Start Timer
              </button>
              <button
                onClick={() => {
                  console.log('🔧 Manual timer stop');
                  setIsCheckedIn(false);
                  setWorkTimer(0);
                }}
                className="w-full py-1 px-3 bg-gray-500 text-white text-sm rounded hover:bg-gray-600"
              >
                🔧 Debug: Stop Timer
              </button>
            </div>
          )}
        </div>

        {/* Quick Stats */}
        <div className="mt-6 pt-6 border-t border-gray-200">
          <div className="grid grid-cols-3 gap-4 text-center">
            <div>
              <div className="text-sm text-gray-600">This Week</div>
              <div className="font-semibold text-gray-900">42:30</div>
            </div>
            <div>
              <div className="text-sm text-gray-600">This Month</div>
              <div className="font-semibold text-gray-900">168:45</div>
            </div>
            <div>
              <div className="text-sm text-gray-600">Overtime</div>
              <div className="font-semibold text-orange-600">8:15</div>
            </div>
          </div>
        </div>
      </div>
    // </PermissionGate>
  );
}

// Compact version for dashboard/header use
export function CompactTimeTracker({ className = '' }) {
  const [isCheckedIn, setIsCheckedIn] = useState(false);
  const [workTimer, setWorkTimer] = useState(0);
  const [loading, setLoading] = useState(false);
  const [currentUser, setCurrentUser] = useState(null);

  useEffect(() => {
    loadCurrentStatus();
  }, []);

  useEffect(() => {
    let interval;
    if (isCheckedIn) {
      interval = setInterval(() => {
        setWorkTimer(prev => prev + 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isCheckedIn]);

  const loadCurrentStatus = async () => {
    try {
      const employeeInfo = await apiService.getMyEmployeeInfo();
      if (!employeeInfo) return;

      setCurrentUser(employeeInfo);
      const status = await apiService.getMyAttendanceStatus();

      if (status && status.is_checked_in) {
        setIsCheckedIn(true);
        if (status.check_in_time) {
          const checkInDate = new Date(status.check_in_time);
          const now = new Date();
          const elapsedSeconds = Math.floor((now - checkInDate) / 1000);
          setWorkTimer(elapsedSeconds);
        }
      }
    } catch (error) {
      console.error('Error loading compact tracker status:', error);
    }
  };

  const handleCheckIn = async () => {
    if (!currentUser) return;

    try {
      setLoading(true);
      await apiService.checkIn(currentUser.id, {
        work_location: 'Office',
        notes: 'Check-in via compact tracker'
      });
      setIsCheckedIn(true);
      setWorkTimer(0);
      await loadCurrentStatus();
    } catch (error) {
      console.error('Compact tracker check-in failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatTimer = (seconds) => {
    const hrs = String(Math.floor(seconds / 3600)).padStart(2, '0');
    const mins = String(Math.floor((seconds % 3600) / 60)).padStart(2, '0');
    const secs = String(seconds % 60).padStart(2, '0');
    return `${hrs}:${mins}:${secs}`;
  };

  return (
    <PermissionGate permission="timeTracker">
      <div className={`flex items-center gap-3 ${className}`}>
        <div className="flex items-center gap-2">
          <div className={`w-2 h-2 rounded-full ${isCheckedIn ? 'bg-green-500' : 'bg-gray-400'}`}></div>
          <span className="text-sm font-medium text-gray-700">
            {formatTimer(workTimer)}
          </span>
        </div>
        
        <button
          onClick={handleCheckIn}
          disabled={loading || !currentUser}
          className={`px-3 py-1 rounded text-sm font-medium disabled:opacity-50 disabled:cursor-not-allowed ${
            isCheckedIn
              ? 'bg-red-100 text-red-700 hover:bg-red-200'
              : 'bg-green-100 text-green-700 hover:bg-green-200'
          }`}
        >
          {loading ? 'Processing...' : (isCheckedIn ? 'Working' : 'Check In')}
        </button>
      </div>
    </PermissionGate>
  );
}
