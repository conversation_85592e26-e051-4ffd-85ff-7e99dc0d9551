/**
 * Enhanced Employee Form Component
 * Features: Multi-step form, validation, professional styling
 * Premium UI/UX with modern design patterns
 */

import React, { useState, useEffect } from 'react';
import {
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Briefcase,
  Building,
  Save,
  X,
  ChevronLeft,
  ChevronRight,
  Check,
  Upload,
  AlertCircle,
  Eye,
  EyeOff
} from 'lucide-react';
import apiService from '../../services/api';

const EmployeeForm = ({ employee = null, onSave, onCancel, isModal = false }) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    // Basic Information
    first_name: '',
    last_name: '',
    middle_name: '',
    email: '',
    phone: '',
    mobile_number: '',
    birth_date: '',
    gender: '',
    
    // Work Information
    employee_id: '',
    department: '',
    position: '',
    hire_date: '',
    employment_type: 'full_time',
    manager_id: '',
    salary: '',
    
    // Address Information
    address: '',
    city: '',
    state: '',
    zip_code: '',
    country: '',
    
    // Emergency Contact
    emergency_contact_name: '',
    emergency_contact_relationship: '',
    emergency_contact_phone: '',
    
    // Additional Information
    skills: [],
    education: '',
    previous_experience: ''
  });
  
  const [errors, setErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [departments, setDepartments] = useState([]);
  const [managers, setManagers] = useState([]);

  const steps = [
    {
      id: 1,
      title: 'Basic Information',
      description: 'Personal details and contact information',
      icon: User
    },
    {
      id: 2,
      title: 'Work Details',
      description: 'Employment and organizational information',
      icon: Briefcase
    },
    {
      id: 3,
      title: 'Address & Emergency',
      description: 'Address and emergency contact details',
      icon: MapPin
    },
    {
      id: 4,
      title: 'Additional Info',
      description: 'Skills, education, and experience',
      icon: Building
    }
  ];

  useEffect(() => {
    if (employee) {
      setFormData({ ...formData, ...employee });
    }
    fetchDepartments();
    fetchManagers();
  }, [employee]);

  const fetchDepartments = async () => {
    try {
      const response = await apiService.get('/departments');
      setDepartments(response || [
        'Engineering',
        'Sales',
        'Marketing',
        'HR',
        'Finance',
        'Operations'
      ]);
    } catch (error) {
      console.error('Error fetching departments:', error);
      setDepartments(['Engineering', 'Sales', 'Marketing', 'HR', 'Finance', 'Operations']);
    }
  };

  const fetchManagers = async () => {
    try {
      const response = await apiService.get('/employees?role=manager');
      setManagers(response || []);
    } catch (error) {
      console.error('Error fetching managers:', error);
      setManagers([]);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  };

  const validateStep = (step) => {
    const newErrors = {};
    
    switch (step) {
      case 1:
        if (!formData.first_name) newErrors.first_name = 'First name is required';
        if (!formData.last_name) newErrors.last_name = 'Last name is required';
        if (!formData.email) newErrors.email = 'Email is required';
        else if (!/\S+@\S+\.\S+/.test(formData.email)) newErrors.email = 'Email is invalid';
        if (!formData.phone) newErrors.phone = 'Phone number is required';
        break;
      case 2:
        if (!formData.employee_id) newErrors.employee_id = 'Employee ID is required';
        if (!formData.department) newErrors.department = 'Department is required';
        if (!formData.position) newErrors.position = 'Position is required';
        if (!formData.hire_date) newErrors.hire_date = 'Hire date is required';
        break;
      case 3:
        if (!formData.address) newErrors.address = 'Address is required';
        if (!formData.emergency_contact_name) newErrors.emergency_contact_name = 'Emergency contact name is required';
        if (!formData.emergency_contact_phone) newErrors.emergency_contact_phone = 'Emergency contact phone is required';
        break;
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, steps.length));
    }
  };

  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleSubmit = async () => {
    if (!validateStep(currentStep)) return;
    
    setLoading(true);
    try {
      if (employee) {
        await apiService.put(`/employee/${employee.id}`, formData);
      } else {
        await apiService.post('/employee', formData);
      }
      onSave && onSave(formData);
    } catch (error) {
      console.error('Error saving employee:', error);
      setErrors({ submit: 'Failed to save employee. Please try again.' });
    } finally {
      setLoading(false);
    }
  };

  const FormField = ({ label, name, type = 'text', required = false, options = null, placeholder = '', className = '' }) => (
    <div className={`space-y-2 ${className}`}>
      <label className="block text-sm font-medium text-gray-700">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      {type === 'select' ? (
        <select
          value={formData[name] || ''}
          onChange={(e) => handleInputChange(name, e.target.value)}
          className={`form-input ${errors[name] ? 'border-red-500' : ''}`}
        >
          <option value="">{placeholder || `Select ${label}`}</option>
          {options?.map((option) => (
            <option key={option.value || option} value={option.value || option}>
              {option.label || option}
            </option>
          ))}
        </select>
      ) : type === 'textarea' ? (
        <textarea
          value={formData[name] || ''}
          onChange={(e) => handleInputChange(name, e.target.value)}
          placeholder={placeholder}
          rows={3}
          className={`form-input ${errors[name] ? 'border-red-500' : ''}`}
        />
      ) : (
        <input
          type={type}
          value={formData[name] || ''}
          onChange={(e) => handleInputChange(name, e.target.value)}
          placeholder={placeholder}
          className={`form-input ${errors[name] ? 'border-red-500' : ''}`}
        />
      )}
      {errors[name] && (
        <p className="text-sm text-red-600 flex items-center space-x-1">
          <AlertCircle size={14} />
          <span>{errors[name]}</span>
        </p>
      )}
    </div>
  );

  const StepIndicator = () => (
    <div className="flex items-center justify-between mb-8">
      {steps.map((step, index) => {
        const Icon = step.icon;
        const isActive = currentStep === step.id;
        const isCompleted = currentStep > step.id;
        
        return (
          <div key={step.id} className="flex items-center">
            <div className="flex flex-col items-center">
              <div className={`w-12 h-12 rounded-full flex items-center justify-center border-2 transition-colors ${
                isCompleted ? 'bg-green-500 border-green-500 text-white' :
                isActive ? 'bg-agno-primary border-agno-primary text-white' :
                'bg-gray-100 border-gray-300 text-gray-400'
              }`}>
                {isCompleted ? <Check size={20} /> : <Icon size={20} />}
              </div>
              <div className="mt-2 text-center">
                <p className={`text-sm font-medium ${isActive ? 'text-agno-primary' : 'text-gray-500'}`}>
                  {step.title}
                </p>
                <p className="text-xs text-gray-400">{step.description}</p>
              </div>
            </div>
            {index < steps.length - 1 && (
              <div className={`flex-1 h-0.5 mx-4 ${
                currentStep > step.id ? 'bg-green-500' : 'bg-gray-200'
              }`} />
            )}
          </div>
        );
      })}
    </div>
  );

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                label="First Name"
                name="first_name"
                required
                placeholder="Enter first name"
              />
              <FormField
                label="Last Name"
                name="last_name"
                required
                placeholder="Enter last name"
              />
            </div>
            
            <FormField
              label="Middle Name"
              name="middle_name"
              placeholder="Enter middle name (optional)"
            />
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                label="Email"
                name="email"
                type="email"
                required
                placeholder="Enter email address"
              />
              <FormField
                label="Phone"
                name="phone"
                type="tel"
                required
                placeholder="Enter phone number"
              />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                label="Mobile Number"
                name="mobile_number"
                type="tel"
                placeholder="Enter mobile number"
              />
              <FormField
                label="Birth Date"
                name="birth_date"
                type="date"
              />
            </div>
            
            <FormField
              label="Gender"
              name="gender"
              type="select"
              options={[
                { value: 'male', label: 'Male' },
                { value: 'female', label: 'Female' },
                { value: 'other', label: 'Other' },
                { value: 'prefer_not_to_say', label: 'Prefer not to say' }
              ]}
            />
          </div>
        );
        
      case 2:
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                label="Employee ID"
                name="employee_id"
                required
                placeholder="Enter employee ID"
              />
              <FormField
                label="Department"
                name="department"
                type="select"
                required
                options={departments}
              />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                label="Position"
                name="position"
                required
                placeholder="Enter job position"
              />
              <FormField
                label="Hire Date"
                name="hire_date"
                type="date"
                required
              />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                label="Employment Type"
                name="employment_type"
                type="select"
                options={[
                  { value: 'full_time', label: 'Full Time' },
                  { value: 'part_time', label: 'Part Time' },
                  { value: 'contract', label: 'Contract' },
                  { value: 'intern', label: 'Intern' }
                ]}
              />
              <FormField
                label="Manager"
                name="manager_id"
                type="select"
                options={managers.map(m => ({ value: m.id, label: `${m.first_name} ${m.last_name}` }))}
                placeholder="Select manager"
              />
            </div>
            
            <FormField
              label="Salary"
              name="salary"
              type="number"
              placeholder="Enter annual salary"
            />
          </div>
        );
        
      case 3:
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Address Information</h3>
            
            <FormField
              label="Address"
              name="address"
              required
              placeholder="Enter street address"
            />
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <FormField
                label="City"
                name="city"
                placeholder="Enter city"
              />
              <FormField
                label="State"
                name="state"
                placeholder="Enter state"
              />
              <FormField
                label="ZIP Code"
                name="zip_code"
                placeholder="Enter ZIP code"
              />
            </div>
            
            <FormField
              label="Country"
              name="country"
              placeholder="Enter country"
            />
            
            <h3 className="text-lg font-semibold text-gray-900 mb-4 mt-8">Emergency Contact</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                label="Contact Name"
                name="emergency_contact_name"
                required
                placeholder="Enter emergency contact name"
              />
              <FormField
                label="Relationship"
                name="emergency_contact_relationship"
                placeholder="Enter relationship"
              />
            </div>
            
            <FormField
              label="Contact Phone"
              name="emergency_contact_phone"
              type="tel"
              required
              placeholder="Enter emergency contact phone"
            />
          </div>
        );
        
      case 4:
        return (
          <div className="space-y-6">
            <FormField
              label="Skills"
              name="skills"
              type="textarea"
              placeholder="Enter skills separated by commas"
            />
            
            <FormField
              label="Education"
              name="education"
              type="textarea"
              placeholder="Enter educational background"
            />
            
            <FormField
              label="Previous Experience"
              name="previous_experience"
              type="textarea"
              placeholder="Enter previous work experience"
            />
          </div>
        );
        
      default:
        return null;
    }
  };

  const FormContainer = ({ children }) => {
    if (isModal) {
      return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl shadow-2xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h2 className="text-2xl font-bold text-gray-900">
                  {employee ? 'Edit Employee' : 'Add New Employee'}
                </h2>
                <button
                  onClick={onCancel}
                  className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
                >
                  <X size={20} />
                </button>
              </div>
            </div>
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
              {children}
            </div>
          </div>
        </div>
      );
    }
    
    return (
      <div className="bg-white rounded-xl shadow-agno border border-gray-100 p-8">
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            {employee ? 'Edit Employee' : 'Add New Employee'}
          </h2>
          <p className="text-gray-600">
            {employee ? 'Update employee information' : 'Enter employee details to add them to the system'}
          </p>
        </div>
        {children}
      </div>
    );
  };

  return (
    <FormContainer>
      <StepIndicator />
      
      <div className="mb-8">
        {renderStepContent()}
      </div>
      
      {errors.submit && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-600 flex items-center space-x-2">
            <AlertCircle size={16} />
            <span>{errors.submit}</span>
          </p>
        </div>
      )}
      
      <div className="flex items-center justify-between pt-6 border-t border-gray-200">
        <button
          onClick={currentStep === 1 ? onCancel : handlePrevious}
          className="btn-secondary flex items-center space-x-2 px-6 py-3 rounded-lg"
        >
          {currentStep === 1 ? <X size={16} /> : <ChevronLeft size={16} />}
          <span>{currentStep === 1 ? 'Cancel' : 'Previous'}</span>
        </button>
        
        <div className="flex items-center space-x-3">
          <span className="text-sm text-gray-500">
            Step {currentStep} of {steps.length}
          </span>
          
          {currentStep < steps.length ? (
            <button
              onClick={handleNext}
              className="btn-agno-primary flex items-center space-x-2 px-6 py-3 rounded-lg"
            >
              <span>Next</span>
              <ChevronRight size={16} />
            </button>
          ) : (
            <button
              onClick={handleSubmit}
              disabled={loading}
              className="btn-agno-primary flex items-center space-x-2 px-6 py-3 rounded-lg disabled:opacity-50"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                <Save size={16} />
              )}
              <span>{loading ? 'Saving...' : (employee ? 'Update Employee' : 'Add Employee')}</span>
            </button>
          )}
        </div>
      </div>
    </FormContainer>
  );
};

export default EmployeeForm;
