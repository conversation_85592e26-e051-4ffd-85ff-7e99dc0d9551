/**
 * Team Collaboration Component
 * Real-time team chat, notifications, and collaborative features
 * Styled consistently with Leave Management components
 */

import React, { useState, useEffect, useRef } from 'react';
import {
  MessageSquare,
  Send,
  Users,
  Bell,
  Settings,
  Search,
  Paperclip,
  Smile,
  MoreVertical,
  Phone,
  Video,
  UserPlus,
  Hash,
  AtSign,
  Clock,
  CheckCircle2
} from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';

const TeamCollaboration = ({ projectId, teamMembers = [] }) => {
  const { user } = useAuth();
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [activeChannel, setActiveChannel] = useState('general');
  const [onlineMembers, setOnlineMembers] = useState([]);
  const [notifications, setNotifications] = useState([]);
  const [showNotifications, setShowNotifications] = useState(false);
  const messagesEndRef = useRef(null);
  const wsRef = useRef(null);

  const channels = [
    { id: 'general', name: 'General', type: 'public', unread: 0 },
    { id: 'development', name: 'Development', type: 'public', unread: 2 },
    { id: 'design', name: 'Design', type: 'public', unread: 0 },
    { id: 'random', name: 'Random', type: 'public', unread: 1 }
  ];

  useEffect(() => {
    loadMessages();
    setupWebSocket();
    
    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
    };
  }, [activeChannel, projectId]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const loadMessages = async () => {
    try {
      // Mock messages data
      const mockMessages = [
        {
          id: 1,
          user: { id: '1', name: 'John Doe', avatar: 'JD', status: 'online' },
          content: 'Hey team! Just finished the authentication module. Ready for review.',
          timestamp: new Date(Date.now() - 60 * 60 * 1000),
          type: 'text',
          reactions: [{ emoji: '👍', count: 2, users: ['Jane', 'Mike'] }]
        },
        {
          id: 2,
          user: { id: '2', name: 'Jane Smith', avatar: 'JS', status: 'online' },
          content: 'Great work! I\'ll review it this afternoon.',
          timestamp: new Date(Date.now() - 45 * 60 * 1000),
          type: 'text',
          reactions: []
        },
        {
          id: 3,
          user: { id: '3', name: 'Mike Johnson', avatar: 'MJ', status: 'away' },
          content: 'Can we schedule a quick call to discuss the API endpoints?',
          timestamp: new Date(Date.now() - 30 * 60 * 1000),
          type: 'text',
          reactions: []
        },
        {
          id: 4,
          user: { id: '1', name: 'John Doe', avatar: 'JD', status: 'online' },
          content: 'Sure! How about 3 PM?',
          timestamp: new Date(Date.now() - 25 * 60 * 1000),
          type: 'text',
          reactions: [{ emoji: '✅', count: 1, users: ['Mike'] }]
        },
        {
          id: 5,
          user: { id: '4', name: 'Sarah Wilson', avatar: 'SW', status: 'online' },
          content: 'I\'ve uploaded the latest design mockups to the shared folder.',
          timestamp: new Date(Date.now() - 10 * 60 * 1000),
          type: 'text',
          attachments: [{ name: 'mockups_v2.fig', size: '2.4 MB', type: 'figma' }],
          reactions: []
        }
      ];
      
      setMessages(mockMessages);
      
      // Mock online members
      setOnlineMembers([
        { id: '1', name: 'John Doe', avatar: 'JD', status: 'online' },
        { id: '2', name: 'Jane Smith', avatar: 'JS', status: 'online' },
        { id: '4', name: 'Sarah Wilson', avatar: 'SW', status: 'online' },
        { id: '3', name: 'Mike Johnson', avatar: 'MJ', status: 'away' }
      ]);
    } catch (error) {
      console.error('Error loading messages:', error);
    }
  };

  const setupWebSocket = () => {
    try {
      const wsUrl = `ws://localhost:8000/ws/chat/${activeChannel}${projectId ? `?project_id=${projectId}` : ''}`;
      const ws = new WebSocket(wsUrl);
      
      ws.onopen = () => {
        console.log('Chat WebSocket connected');
      };
      
      ws.onmessage = (event) => {
        const message = JSON.parse(event.data);
        setMessages(prev => [...prev, message]);
      };
      
      ws.onclose = () => {
        console.log('Chat WebSocket disconnected');
      };
      
      ws.onerror = (error) => {
        console.error('Chat WebSocket error:', error);
      };
      
      wsRef.current = ws;
    } catch (error) {
      console.error('Error setting up chat WebSocket:', error);
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleSendMessage = (e) => {
    e.preventDefault();
    
    if (!newMessage.trim()) return;
    
    const message = {
      id: Date.now(),
      user: { 
        id: user?.id || 'current', 
        name: user?.name || 'You', 
        avatar: user?.name?.split(' ').map(n => n[0]).join('') || 'YU',
        status: 'online' 
      },
      content: newMessage,
      timestamp: new Date(),
      type: 'text',
      reactions: []
    };
    
    // Send via WebSocket
    if (wsRef.current && wsRef.current.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message));
    } else {
      // Fallback: add to local state
      setMessages(prev => [...prev, message]);
    }
    
    setNewMessage('');
  };

  const formatMessageTime = (timestamp) => {
    const now = new Date();
    const messageDate = new Date(timestamp);
    const diffInMinutes = Math.floor((now - messageDate) / 60000);
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return messageDate.toLocaleDateString();
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'online': return 'bg-green-500';
      case 'away': return 'bg-yellow-500';
      case 'busy': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 h-[600px] flex">
      {/* Sidebar */}
      <div className="w-64 border-r border-gray-200 flex flex-col">
        {/* Channels Header */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center justify-between mb-3">
            <h3 className="font-semibold text-gray-900">Channels</h3>
            <button className="text-gray-400 hover:text-gray-600">
              <Settings size={16} />
            </button>
          </div>
          
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={14} />
            <input
              type="text"
              placeholder="Search channels..."
              className="w-full pl-8 pr-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
        </div>
        
        {/* Channels List */}
        <div className="flex-1 overflow-y-auto p-2">
          {channels.map((channel) => (
            <button
              key={channel.id}
              onClick={() => setActiveChannel(channel.id)}
              className={`w-full flex items-center justify-between p-2 rounded-lg text-left hover:bg-gray-100 transition-colors ${
                activeChannel === channel.id ? 'bg-blue-100 text-blue-600' : 'text-gray-700'
              }`}
            >
              <div className="flex items-center space-x-2">
                <Hash size={16} />
                <span className="text-sm font-medium">{channel.name}</span>
              </div>
              {channel.unread > 0 && (
                <span className="bg-red-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">
                  {channel.unread}
                </span>
              )}
            </button>
          ))}
        </div>
        
        {/* Online Members */}
        <div className="border-t border-gray-200 p-4">
          <h4 className="text-sm font-medium text-gray-900 mb-3">Online ({onlineMembers.length})</h4>
          <div className="space-y-2">
            {onlineMembers.slice(0, 5).map((member) => (
              <div key={member.id} className="flex items-center space-x-2">
                <div className="relative">
                  <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs">
                    {member.avatar}
                  </div>
                  <div className={`absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-white ${getStatusColor(member.status)}`}></div>
                </div>
                <span className="text-sm text-gray-700 truncate">{member.name}</span>
              </div>
            ))}
            {onlineMembers.length > 5 && (
              <button className="text-xs text-blue-600 hover:text-blue-800">
                +{onlineMembers.length - 5} more
              </button>
            )}
          </div>
        </div>
      </div>
      
      {/* Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Chat Header */}
        <div className="p-4 border-b border-gray-200 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Hash size={20} className="text-gray-600" />
            <h3 className="font-semibold text-gray-900">{channels.find(c => c.id === activeChannel)?.name}</h3>
            <span className="text-sm text-gray-500">({onlineMembers.length} members)</span>
          </div>
          
          <div className="flex items-center space-x-2">
            <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg">
              <Phone size={16} />
            </button>
            <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg">
              <Video size={16} />
            </button>
            <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg">
              <UserPlus size={16} />
            </button>
            <button className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg">
              <MoreVertical size={16} />
            </button>
          </div>
        </div>
        
        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-4">
          {messages.map((message) => (
            <div key={message.id} className="flex items-start space-x-3 group">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs font-medium">
                  {message.user.avatar}
                </div>
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2 mb-1">
                  <span className="font-medium text-gray-900 text-sm">{message.user.name}</span>
                  <span className="text-xs text-gray-500">{formatMessageTime(message.timestamp)}</span>
                </div>
                
                <div className="text-sm text-gray-700 mb-2">{message.content}</div>
                
                {/* Attachments */}
                {message.attachments && message.attachments.length > 0 && (
                  <div className="space-y-2 mb-2">
                    {message.attachments.map((attachment, index) => (
                      <div key={index} className="flex items-center space-x-2 p-2 bg-gray-50 rounded-lg border">
                        <Paperclip size={14} className="text-gray-400" />
                        <span className="text-sm text-gray-700">{attachment.name}</span>
                        <span className="text-xs text-gray-500">({attachment.size})</span>
                      </div>
                    ))}
                  </div>
                )}
                
                {/* Reactions */}
                {message.reactions && message.reactions.length > 0 && (
                  <div className="flex items-center space-x-2">
                    {message.reactions.map((reaction, index) => (
                      <button
                        key={index}
                        className="flex items-center space-x-1 px-2 py-1 bg-gray-100 hover:bg-gray-200 rounded-full text-xs transition-colors"
                        title={`${reaction.users.join(', ')} reacted with ${reaction.emoji}`}
                      >
                        <span>{reaction.emoji}</span>
                        <span className="text-gray-600">{reaction.count}</span>
                      </button>
                    ))}
                    <button className="p-1 text-gray-400 hover:text-gray-600 opacity-0 group-hover:opacity-100 transition-opacity">
                      <Smile size={14} />
                    </button>
                  </div>
                )}
              </div>
            </div>
          ))}
          <div ref={messagesEndRef} />
        </div>
        
        {/* Message Input */}
        <div className="p-4 border-t border-gray-200">
          <form onSubmit={handleSendMessage} className="flex items-center space-x-2">
            <div className="flex-1 relative">
              <input
                type="text"
                value={newMessage}
                onChange={(e) => setNewMessage(e.target.value)}
                placeholder={`Message #${channels.find(c => c.id === activeChannel)?.name}`}
                className="w-full px-4 py-2 pr-20 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center space-x-1">
                <button
                  type="button"
                  className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <Paperclip size={16} />
                </button>
                <button
                  type="button"
                  className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <Smile size={16} />
                </button>
              </div>
            </div>
            <button
              type="submit"
              disabled={!newMessage.trim()}
              className="p-2 agno-bg-primary text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Send size={16} />
            </button>
          </form>
          
          <div className="mt-2 text-xs text-gray-500">
            Press Enter to send, Shift+Enter for new line
          </div>
        </div>
      </div>
    </div>
  );
};

export default TeamCollaboration;
